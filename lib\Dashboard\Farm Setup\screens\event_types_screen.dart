import 'package:flutter/material.dart';
import '../services/farm_setup_repository.dart';
import 'package:get_it/get_it.dart';
import '../dialogs/event_type_dialog.dart';
import '../../../Dashboard/Events/models/event_type_isar.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_tabs.dart';
import '../../../utils/message_utils.dart';

class ErrorMessage extends StatelessWidget {
  final String message;

  const ErrorMessage({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          message,
          style: TextStyle(color: Theme.of(context).colorScheme.error),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class EventTypesScreen extends StatefulWidget {
  const EventTypesScreen({Key? key}) : super(key: key);

  @override
  State<EventTypesScreen> createState() => _EventTypesScreenState();
}

class _EventTypesScreenState extends State<EventTypesScreen>
    with SingleTickerProviderStateMixin {
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  bool _isLoading = true;
  String? _errorMessage;
  List<EventTypeIsar> _eventTypes = [];
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _loadEventTypes();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadEventTypes() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final eventTypes = await _farmSetupRepository.getEventTypes();
      setState(() {
        _eventTypes = eventTypes;
        _isLoading = false;
      });
      _animationController.forward();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load event types: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _addEventType() async {
    try {
      final result = await showDialog<EventTypeIsar>(
        context: context,
        builder: (context) => const EventTypeDialog(),
      );

      if (result != null) {
        setState(() {
          _eventTypes.add(result);
          // Sort the list if needed
          _eventTypes.sort((a, b) => a.name.compareTo(b.name));
        });

        if (mounted) {
          FarmSetupMessageUtils.showSuccess(context,
              'Event type "${result.name}" added successfully');
        }
      }
    } catch (e) {
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Failed to add event type: $e');
      }
    }
  }

  Future<void> _editEventType(EventTypeIsar eventType) async {
    try {
      final result = await showDialog<EventTypeIsar>(
        context: context,
        builder: (context) => EventTypeDialog(eventType: eventType),
      );

      if (result != null) {
        setState(() {
          final index = _eventTypes.indexWhere((et) => et.id == result.id);
          if (index != -1) {
            _eventTypes[index] = result;
          }
          // Sort the list if needed
          _eventTypes.sort((a, b) => a.name.compareTo(b.name));
        });

        if (mounted) {
          FarmSetupMessageUtils.showSuccess(context,
              'Event type "${result.name}" updated successfully');
        }
      }
    } catch (e) {
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Failed to update event type: $e');
      }
    }
  }

  Future<void> _deleteEventType(EventTypeIsar eventType) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event Type'),
        content: Text('Are you sure you want to delete "${eventType.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _farmSetupRepository.deleteEventType(eventType.id);

        setState(() {
          _eventTypes.removeWhere((et) => et.id == eventType.id);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Event type "${eventType.name}" deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to delete event type: $e')),
          );
        }
      }
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: kSpacingMedium),
          Text(
            'No event types found',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: kSpacingSmall),
          const Text(
            'Add event types to categorize events in your farm',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: kSpacingMedium),
          ElevatedButton.icon(
            onPressed: _addEventType,
            icon: const Icon(Icons.add),
            label: const Text('Add Event Type'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Event Types'),
        backgroundColor: AppColors.primaryColor,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _errorMessage != null
              ? ErrorMessage(message: _errorMessage!)
              : _eventTypes.isEmpty
                  ? _buildEmptyState()
                  : AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return ListView.builder(
                          itemCount: _eventTypes.length,
                          itemBuilder: (context, index) {
                            final eventType = _eventTypes[index];
                            final animation =
                                Tween(begin: 0.0, end: 1.0).animate(
                              CurvedAnimation(
                                parent: _animationController,
                                curve: Interval(
                                  index / _eventTypes.length,
                                  (index + 1) / _eventTypes.length,
                                  curve: Curves.easeInOut,
                                ),
                              ),
                            );

                            return FadeTransition(
                              opacity: animation,
                              child: SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0.5, 0),
                                  end: Offset.zero,
                                ).animate(animation),
                                child: Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: kSpacingMedium,
                                    vertical: kSpacingSmall / 2,
                                  ),
                                  child: ListTile(
                                    leading: CircleAvatar(
                                      backgroundColor: eventType.colorValue !=
                                              null
                                          ? eventType.color.withAlpha(51)
                                          : AppColors.accentColor.withAlpha(51),
                                      child: Icon(
                                        Icons.event,
                                        color: eventType.colorValue != null
                                            ? eventType.color
                                            : AppColors.accentColor,
                                      ),
                                    ),
                                    title: Text(
                                      eventType.name,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium,
                                    ),
                                    subtitle:
                                        eventType.description?.isNotEmpty ==
                                                true
                                            ? Text(
                                                eventType.description!,
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium,
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              )
                                            : null,
                                    trailing: PopupMenuButton<String>(
                                      onSelected: (value) {
                                        if (value == 'edit') {
                                          _editEventType(eventType);
                                        } else if (value == 'delete') {
                                          _deleteEventType(eventType);
                                        }
                                      },
                                      itemBuilder: (context) => [
                                        const PopupMenuItem(
                                          value: 'edit',
                                          child: Row(
                                            children: [
                                              Icon(Icons.edit),
                                              SizedBox(width: kSpacingSmall),
                                              Text('Edit'),
                                            ],
                                          ),
                                        ),
                                        const PopupMenuItem(
                                          value: 'delete',
                                          child: Row(
                                            children: [
                                              Icon(Icons.delete,
                                                  color: Colors.red),
                                              SizedBox(width: kSpacingSmall),
                                              Text('Delete',
                                                  style: TextStyle(
                                                      color: Colors.red)),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
      floatingActionButton: _isLoading || _errorMessage != null
          ? null
          : UniversalFAB.add(
              onPressed: _addEventType,
              tooltip: 'Add Event Type',
            ),
    );
  }
}
