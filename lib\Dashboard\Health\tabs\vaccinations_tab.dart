import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../controllers/health_controller.dart';
import '../models/vaccination_record_isar.dart';
import '../details/health_details_screen.dart';
import '../../widgets/universal_record_card.dart';
import '../../widgets/filters/filter_layout.dart';
import '../../widgets/filters/filters.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';

import '../dialogs/vaccination_form_dialog.dart';

class VaccinationsTab extends StatefulWidget {
  final HealthController? controller; // Made optional to support Provider pattern

  const VaccinationsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<VaccinationsTab> createState() => _VaccinationsTabState();
}

class _VaccinationsTabState extends State<VaccinationsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  HealthController get _controller => widget.controller ?? context.read<HealthController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  void _onFiltersChanged() {
    final filterState = HealthFilterState(
      searchQuery: _filterController.searchQuery,
      startDate: _filterController.startDate,
      endDate: _filterController.endDate,
      cattleId: _filterController.globalFilters['cattle'],
      vaccineType: _filterController.globalFilters['vaccineType'],
    );
    _controller.applyFilters(filterState);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<HealthController>(
      builder: (context, controller, child) {
        final vaccinations = controller.vaccinations;
        final isCompletelyEmpty = controller.unfilteredVaccinations.isEmpty;

        return Column(
          children: [
            // Filter Layout
            UniversalFilterLayout(
              controller: _filterController,
              theme: FilterTheme.health,
              moduleName: 'vaccinations',
              sortFields: const [...SortField.commonFields, ...SortField.healthFields],
              searchHint: 'Search vaccinations...',
              totalCount: controller.unfilteredVaccinations.length,
              filteredCount: controller.vaccinations.length,
              config: FilterLayoutConfig.standard,
            ),

            // Vaccinations List
            Expanded(
              child: isCompletelyEmpty
                  ? UniversalTabEmptyState.forTab(
                      title: 'No Vaccination Records',
                      message: 'Add your first vaccination record to start tracking vaccinations.',
                      tabColor: AppColors.healthHeader,
                      tabIndex: 3, // Vaccinations tab
                      action: TabEmptyStateActions.addFirstRecord(
                        onPressed: () => _showAddVaccinationDialog(),
                        tabColor: AppColors.healthHeader,
                      ),
                    )
                  : vaccinations.isEmpty
                      ? UniversalTabEmptyState.forTab(
                          title: 'No Matching Records',
                          message: 'No vaccination records match your current filters. Try adjusting your search criteria.',
                          tabColor: AppColors.healthHeader,
                          tabIndex: 3, // Vaccinations tab
                          action: TabEmptyStateActions.clearFilters(
                            onPressed: () => _filterController.clearAllApplied(),
                            tabColor: AppColors.healthHeader,
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.only(bottom: 80), // Prevent FAB overlap
                          itemCount: vaccinations.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                              child: _buildVaccinationCard(vaccinations[index]),
                            );
                          },
                        ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildVaccinationCard(VaccinationIsar record) {
    final cattle = _controller.getCattle(record.cattleTagId);

    // Format row 1: Date + Cattle name with tag ID
    String dateText = record.date != null
        ? DateFormat('MMM dd, yyyy').format(record.date!)
        : 'No date';

    String cattleName = cattle?.name ?? 'Unknown Cattle';
    if (cattle?.tagId != null && cattle!.tagId!.isNotEmpty) {
      cattleName += ' (${cattle.tagId!.toUpperCase()})';
    }

    // Format row 2: Vaccine name + Cost
    String vaccineName = record.vaccineName ?? 'Unknown Vaccine';
    String costText = record.cost != null && record.cost! > 0
        ? '\$${record.cost!.toStringAsFixed(2)}'
        : 'No cost';

    // Check if we have optional data for row 3
    bool hasBatch = record.batchNumber?.isNotEmpty == true;
    bool hasManufacturer = record.manufacturer?.isNotEmpty == true;

    return UniversalRecordCard(
      // Row 1: Date + Cattle name (Tag ID)
      row1Left: dateText,
      row1Right: cattleName,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.pets,

      // Row 2: Vaccine name + Cost
      row2Left: vaccineName,
      row2Right: costText,
      row2LeftIcon: Icons.vaccines,
      row2RightIcon: Icons.receipt_long,

      // Row 3: Batch + Manufacturer (conditional)
      row3Left: hasBatch ? 'Batch: ${record.batchNumber!}' : null,
      row3Right: hasManufacturer ? record.manufacturer! : null,
      row3LeftIcon: hasBatch ? Icons.qr_code : null,
      row3RightIcon: hasManufacturer ? Icons.business : null,

      // Notes
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.healthHeader,

      onTap: () => _showVaccinationDetails(record),
      onEdit: () => _editVaccination(record),
      onDelete: () => _deleteVaccination(record),
    );
  }

  void _showAddVaccinationDialog() {
    if (_controller.cattle.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No cattle available for vaccination records.'),
          duration: Duration(milliseconds: 1000),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => VaccinationFormDialog(
        cattle: _controller.cattle,
        cattleId: _controller.cattle.first.tagId ?? '',
        onSave: (record) async {
          await _controller.addVaccinationRecord(record);
          return true;
        },
      ),
    );
  }

  void _showVaccinationDetails(VaccinationIsar record) {
    print('🔍 VACCINATION: Attempting navigation for record ${record.id}');
    print('🔍 VACCINATION: Record cattleTagId: "${record.cattleTagId}"');

    // Handle null or empty cattle tag ID
    final cattleTagId = record.cattleTagId;
    if (cattleTagId == null || cattleTagId.isEmpty) {
      print('❌ VACCINATION: No cattle tag ID associated with this vaccination record');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This vaccination record is not associated with any cattle. Please edit the record to assign it to a cattle.'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 4),
        ),
      );
      return;
    }

    final cattle = _controller.getCattle(cattleTagId);
    if (cattle == null) {
      print('❌ VACCINATION: No cattle found with tagId: "$cattleTagId"');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Cattle with tag ID "$cattleTagId" not found. The cattle may have been deleted.'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    print('✅ VACCINATION: Navigate to detail for cattle ${cattle.name} (${cattle.tagId})');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HealthDetailsScreen(
          cattle: cattle,
        ),
      ),
    );
  }

  void _editVaccination(VaccinationIsar record) {
    print('🔧 VACCINATIONS TAB: Opening edit dialog for vaccination ${record.id}');
    showDialog(
      context: context,
      builder: (context) => VaccinationFormDialog(
        cattle: _controller.cattle,
        cattleId: record.cattleId ?? '',
        vaccination: record,
        onSave: (updatedRecord) async {
          try {
            print('💾 VACCINATIONS TAB: Starting update for vaccination ${updatedRecord.id}');
            print('📊 VACCINATIONS TAB: Before update - Current vaccinations count: ${_controller.vaccinations.length}');

            await _controller.updateVaccinationRecord(updatedRecord);

            print('✅ VACCINATIONS TAB: Update completed for vaccination ${updatedRecord.id}');
            print('📊 VACCINATIONS TAB: After update - Current vaccinations count: ${_controller.vaccinations.length}');

            return true;
          } catch (e) {
            print('❌ VACCINATIONS TAB: Update failed for vaccination ${updatedRecord.id}: $e');
            return false;
          }
        },
      ),
    );
  }

  void _deleteVaccination(VaccinationIsar record) {
    print('🗑️ VACCINATIONS TAB: Opening delete confirmation for vaccination ${record.id}');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Vaccination'),
        content: const Text('Are you sure you want to delete this vaccination record?'),
        actions: [
          TextButton(
            onPressed: () {
              print('❌ VACCINATIONS TAB: Delete cancelled for vaccination ${record.id}');
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                print('🗑️ VACCINATIONS TAB: Starting delete for vaccination ${record.id}');
                print('📊 VACCINATIONS TAB: Before delete - Current vaccinations count: ${_controller.vaccinations.length}');

                await _controller.deleteVaccinationRecord(record.id);

                print('✅ VACCINATIONS TAB: Delete completed for vaccination ${record.id}');
                print('📊 VACCINATIONS TAB: After delete - Current vaccinations count: ${_controller.vaccinations.length}');
              } catch (e) {
                print('❌ VACCINATIONS TAB: Delete failed for vaccination ${record.id}: $e');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }


}