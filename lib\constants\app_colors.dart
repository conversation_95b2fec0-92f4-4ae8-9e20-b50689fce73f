import 'package:flutter/material.dart';

class AppColors {
  // ============================================================================
  // GLOBAL APP COLORS
  // ============================================================================

  /// Primary app colors used throughout the application
  static const Color primary = Color(0xFF2E7D32);
  static const Color primaryColor = Color(0xFF2E7D32);
  static const Color accent = Color(0xFF4CAF50);
  static const Color accentColor = Color(0xFF4CAF50);
  static const Color background = Color(0xFFF5F5F5);
  static const Color error = Colors.red;
  static const Color success = Color(0xFF2E7D32);
  static const Color fallback = Color(0xFF2196F3); // Blue

  // Material Design color system compatibility
  static const Color secondary = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF000000);
  static const Color onBackground = Color(0xFF000000);

  // ============================================================================
  // MAIN DASHBOARD COLORS
  // ============================================================================

  /// Main dashboard specific colors
  static const Color dashboardHeader = Color(0xFF2E7D32); // Primary green
  static const Color dashboardCard = Color(0xFFF5F5F5); // Light background

  // ============================================================================
  // CATTLE MODULE COLORS
  // ============================================================================

  /// Cattle module header and primary color
  static const Color cattleHeader = Color(0xFF1976D2); // Blue

  /// Cattle Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> cattleKpiColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF388E3C), // Green
    Color(0xFF7B1FA2), // Purple
    Color(0xFFD32F2F), // Red
    Color(0xFF00796B), // Teal
    Color(0xFFE91E63), // Pink
  ];

  /// Cattle Analytics - Age Demographics Colors (4 colors for age breakdown)
  static const List<Color> cattleAgeInsightColors = [
    Color(0xFF2196F3), // Light Blue
    Color(0xFF5E35B1), // Deep Purple
    Color(0xFFE53935), // Red
    Color(0xFF00796B), // Teal
  ];

  /// Cattle Analytics - Gender Distribution Colors
  static const Map<String, Color> cattleGenderColors = {
    'Male': Color(0xFF1976D2), // Blue
    'Female': Color(0xFFE91E63), // Pink
    'Unknown': Color(0xFF7B1FA2), // Purple
  };

  /// Cattle Analytics - Animal Type Colors
  static const Map<String, Color> cattleAnimalTypeColors = {
    'Cow': Color(0xFF388E3C), // Green
    'Bull': Color(0xFFD32F2F), // Red
    'Calf': Color(0xFF7B1FA2), // Purple
    'Heifer': Color(0xFF00796B), // Teal
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Cattle Analytics - Age Distribution Colors (for pie charts)
  static const Map<String, Color> cattleAgeDistributionColors = {
    '0-6 months': Color(0xFF4CAF50), // Green
    '6-12 months': Color(0xFF2196F3), // Blue
    '1-2 years': Color(0xFF9C27B0), // Purple
    '2-5 years': Color(0xFF00796B), // Teal
    '5+ years': Color(0xFFE91E63), // Pink
  };

  /// Cattle Analytics - Section-specific colors
  static const Color cattleKpiSection = Color(0xFF1976D2); // Blue
  static const Color cattleAgeDemographics = Color(0xFF7B1FA2); // Purple
  static const Color cattleFinancialOverview = Color(0xFFD32F2F); // Red
  static const Color cattleHerdComposition = Color(0xFF388E3C); // Green

  // ============================================================================
  // BREEDING MODULE COLORS
  // ============================================================================

  /// Breeding module header and primary color
  static const Color breedingHeader = Color(0xFF7B1FA2); // Purple

  /// Breeding Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> breedingKpiColors = [
    Color(0xFF7B1FA2), // Purple
    Color(0xFF388E3C), // Green
    Color(0xFFE91E63), // Pink
    Color(0xFFD32F2F), // Red
    Color(0xFF00796B), // Teal
    Color(0xFF1976D2), // Blue
  ];

  /// Breeding Analytics - Status Colors
  static const Map<String, Color> breedingStatusColors = {
    'Open': Color(0xFF388E3C), // Green
    'Bred': Color(0xFF7B1FA2), // Purple
    'Pregnant': Color(0xFFE91E63), // Pink
    'Fresh': Color(0xFF1976D2), // Blue
    'Active': Color(0xFFFF9800), // Orange
    'Completed': Color(0xFF388E3C), // Green
    'Pending': Color(0xFF1976D2), // Blue
    'Failed': Color(0xFFD32F2F), // Red
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Breeding Analytics - Pregnancy Status Colors
  static const Map<String, Color> breedingPregnancyStatusColors = {
    'Active': Color(0xFF7B1FA2), // Purple
    'Completed': Color(0xFF388E3C), // Green
    'Confirmed': Color(0xFF1976D2), // Blue
    'Failed': Color(0xFFD32F2F), // Red
    'Unknown': Color(0xFF424242), // Dark grey
  };

  // ============================================================================
  // HEALTH MODULE COLORS
  // ============================================================================

  /// Health module header and primary color
  static const Color healthHeader = Color(0xFFD32F2F); // Red

  /// Health Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> healthKpiColors = [
    Color(0xFFD32F2F), // Red
    Color(0xFF388E3C), // Green
    Color(0xFFFF9800), // Orange
    Color(0xFF1976D2), // Blue
    Color(0xFF7B1FA2), // Purple
    Color(0xFF00796B), // Teal
  ];

  /// Health Analytics - Record Type Colors
  static const Map<String, Color> healthRecordTypeColors = {
    'Treatment': Color(0xFFFF9800), // Orange
    'Vaccination': Color(0xFF388E3C), // Green
    'Checkup': Color(0xFF1976D2), // Blue
    'Surgery': Color(0xFFD32F2F), // Red
    'Medication': Color(0xFF7B1FA2), // Purple
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Health Analytics - Status Colors
  static const Map<String, Color> healthStatusColors = {
    'Active': Color(0xFFFF9800), // Orange
    'Completed': Color(0xFF388E3C), // Green
    'Pending': Color(0xFF1976D2), // Blue
    'Cancelled': Color(0xFFD32F2F), // Red
    'Resolved': Color(0xFF388E3C), // Green
    'Unknown': Color(0xFF424242), // Dark grey
  };

  // ============================================================================
  // EVENTS MODULE COLORS
  // ============================================================================

  /// Events module header and primary color
  static const Color eventsHeader = Color(0xFF9C27B0); // Purple
  static const Color eventsColor = Color(0xFF9C27B0); // Purple (alias for compatibility)

  /// Events Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> eventsKpiColors = [
    Color(0xFF9C27B0), // Purple
    Color(0xFF388E3C), // Green
    Color(0xFFD32F2F), // Red
    Color(0xFF1976D2), // Blue
    Color(0xFF00796B), // Teal
    Color(0xFFE91E63), // Pink
  ];

  /// Events Analytics - Priority Colors
  static const Map<String, Color> eventsPriorityColors = {
    'High': Color(0xFFD32F2F), // Red
    'Medium': Color(0xFFFF9800), // Orange
    'Low': Color(0xFF388E3C), // Green
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Events Analytics - Status Colors
  static const Map<String, Color> eventsStatusColors = {
    'Completed': Color(0xFF388E3C), // Green
    'Pending': Color(0xFFFF9800), // Orange
    'Overdue': Color(0xFFD32F2F), // Red
    'Cancelled': Color(0xFF424242), // Dark grey
    'Unknown': Color(0xFF424242), // Dark grey
  };

  // ============================================================================
  // MILK MODULE COLORS
  // ============================================================================

  /// Milk module header and primary color
  static const Color milkHeader = Color(0xFF00796B); // Teal

  /// Milk Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> milkKpiColors = [
    Color(0xFF00796B), // Teal
    Color(0xFF388E3C), // Green
    Color(0xFF1976D2), // Blue
    Color(0xFFFF9800), // Orange
    Color(0xFF7B1FA2), // Purple
    Color(0xFFD32F2F), // Red
  ];

  /// Milk Analytics - Production Session Colors
  static const Map<String, Color> milkSessionColors = {
    'Morning': Color(0xFFFFEB3B), // Yellow
    'Afternoon': Color(0xFFFF9800), // Orange
    'Evening': Color(0xFF3F51B5), // Indigo
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Milk Analytics - Quality Colors
  static const Map<String, Color> milkQualityColors = {
    'Excellent': Color(0xFF388E3C), // Green
    'Good': Color(0xFF689F38), // Light green
    'Average': Color(0xFFFF9800), // Orange
    'Poor': Color(0xFFD32F2F), // Red
    'Unknown': Color(0xFF424242), // Dark grey
  };

  // ============================================================================
  // WEIGHT MODULE COLORS
  // ============================================================================

  /// Weight module header and primary color
  static const Color weightHeader = Color(0xFF388E3C); // Green

  /// Weight Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> weightKpiColors = [
    Color(0xFF388E3C), // Green
    Color(0xFF1976D2), // Blue
    Color(0xFFFF9800), // Orange
    Color(0xFF7B1FA2), // Purple
    Color(0xFF00796B), // Teal
    Color(0xFFD32F2F), // Red
  ];

  /// Weight Analytics - Growth Trend Colors
  static const Map<String, Color> weightTrendColors = {
    'Increasing': Color(0xFF388E3C), // Green
    'Stable': Color(0xFF1976D2), // Blue
    'Decreasing': Color(0xFFD32F2F), // Red
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Weight Analytics - Performance Colors
  static const Map<String, Color> weightPerformanceColors = {
    'Excellent': Color(0xFF388E3C), // Green
    'Good': Color(0xFF689F38), // Light green
    'Average': Color(0xFFFF9800), // Orange
    'Poor': Color(0xFFD32F2F), // Red
    'Unknown': Color(0xFF424242), // Dark grey
  };

  // ============================================================================
  // TRANSACTION MODULE COLORS
  // ============================================================================

  /// Transaction module header and primary color
  static const Color transactionHeader = Color(0xFFE91E63); // Pink

  /// Transaction Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> transactionKpiColors = [
    Color(0xFFE91E63), // Pink
    Color(0xFF388E3C), // Green
    Color(0xFFD32F2F), // Red
    Color(0xFF1976D2), // Blue
    Color(0xFFFF9800), // Orange
    Color(0xFF7B1FA2), // Purple
  ];

  /// Transaction Analytics - Type Colors
  static const Map<String, Color> transactionTypeColors = {
    'Income': Color(0xFF388E3C), // Green
    'Expense': Color(0xFFD32F2F), // Red
    'Transfer': Color(0xFF1976D2), // Blue
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Transaction Analytics - Category Colors
  static const Map<String, Color> transactionCategoryColors = {
    'Feed': Color(0xFF388E3C), // Green
    'Medical': Color(0xFFD32F2F), // Red
    'Equipment': Color(0xFF1976D2), // Blue
    'Labor': Color(0xFFFF9800), // Orange
    'Sales': Color(0xFF00796B), // Teal
    'Other': Color(0xFF7B1FA2), // Purple
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Transaction Analytics - Payment Method Colors
  static const Map<String, Color> transactionPaymentMethodColors = {
    'Cash': Color(0xFF388E3C), // Green
    'Card': Color(0xFF1976D2), // Blue
    'Bank Transfer': Color(0xFF7B1FA2), // Purple
    'Check': Color(0xFFFF9800), // Orange
    'Other': Color(0xFFD32F2F), // Red
    'Unknown': Color(0xFF424242), // Dark grey
  };
}