import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../../Dashboard/Events/models/event_type_isar.dart';
import '../services/farm_setup_repository.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

// --- Constants ---
class _AppStrings {
  static const String addEventTypeTitle = 'Add Event Type';
  static const String editEventTypeTitle = 'Edit Event Type';
  static const String nameLabel = 'Event Type Name';
  static const String descriptionLabel = 'Description (Optional)';

  // Validation messages

  // Success messages
  static const String addSuccess = 'Event type added successfully';
  static const String updateSuccess = 'Event type updated successfully';
  static const String saveError = 'Error saving event type';
}

class EventTypeDialog extends StatefulWidget {
  final EventTypeIsar? eventType;

  const EventTypeDialog({Key? key, this.eventType}) : super(key: key);

  @override
  State<EventTypeDialog> createState() => _EventTypeDialogState();
}

class _EventTypeDialogState extends State<EventTypeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  late Color _selectedColor;


  @override
  void initState() {
    super.initState();
    if (widget.eventType != null) {
      _nameController.text = widget.eventType!.name;
      _descriptionController.text = widget.eventType?.description ?? '';
      _selectedColor = widget.eventType!.color;
    } else {
      _selectedColor = Colors.blue;
    }
  }

  Future<void> _handleSubmit() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }



    try {
      final eventType = widget.eventType ?? EventTypeIsar();
      eventType.name = _nameController.text;
      eventType.description = _descriptionController.text;
      eventType.color = _selectedColor;

      if (widget.eventType == null) {
        eventType.id = 0; // Let Isar auto-increment
        eventType.businessId = const Uuid().v4();
        eventType.createdAt = DateTime.now();
      }

      eventType.updatedAt = DateTime.now();

      await _farmSetupRepository.saveEventType(eventType);

      if (mounted) {
        Navigator.of(context).pop(eventType);
      }
    } catch (e) {
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Error saving event type: $e');
      }
    } finally {
      if (mounted) {

      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.eventType == null
        ? UniversalFormDialog(
            title: _AppStrings.addEventTypeTitle,
            headerIcon: Icons.event_note, // Event type-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editEventTypeTitle,
            headerIcon: Icons.event_note, // Event type-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Name Field
          UniversalFormField.textField(
            label: _AppStrings.nameLabel,
            controller: _nameController,
            prefixIcon: Icons.label,
            prefixIconColor: Colors.blue,
            validator: (value) => UniversalFormField.requiredValidator(value, 'name'),
          ),
          UniversalFormField.spacing,

          // Description Field
          UniversalFormField.multilineField(
            label: _AppStrings.descriptionLabel,
            controller: _descriptionController,
            maxLines: 3,
            prefixIcon: Icons.description,
            prefixIconColor: Colors.green,
          ),
          UniversalFormField.spacing,

          // Color Picker Section
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _selectedColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade300),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Pick a color'),
                          content: SingleChildScrollView(
                            child: BlockPicker(
                              pickerColor: _selectedColor,
                              onColorChanged: (Color color) {
                                setState(() {
                                  _selectedColor = color;
                                });
                                Navigator.of(context).pop();
                              },
                            ),
                          ),
                        );
                      },
                    );
                  },
                  icon: const Icon(Icons.color_lens),
                  label: const Text('Choose Color'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;



    try {
      await _handleSubmit();

      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.eventType != null
              ? _AppStrings.updateSuccess
              : _AppStrings.addSuccess,
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, '${_AppStrings.saveError}: $e');
      }
    } finally {
      if (mounted) {

      }
    }
  }
}
