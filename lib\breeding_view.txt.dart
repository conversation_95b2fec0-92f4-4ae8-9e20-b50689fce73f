// LEGACY FILE - COMMENTED OUT FOR ANALYSIS PURPOSES
// This file contains legacy breeding implementation for reference
// DO NOT UNCOMMENT - Use for feature analysis only

/*
import 'package:flutter/material.dart';
import 'dart:async';
import '../../models/cattle_isar.dart';
import 'package:cattle_manager/services/database/database_helper.dart';
import '../../../Breeding/dialogs/breeding_form_dialog.dart';
import '../../widgets/breeding_history_card.dart';
import '../../widgets/eligibility_card.dart';
import '../../widgets/stats_card.dart';
import '../../../Breeding/models/breeding_record_isar.dart';

*/

// END OF LEGACY FILE COMMENT BLOCK

/*
class BreedingView extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar) onCattleUpdated;

  const BreedingView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
  });

  @override
  State<BreedingView> createState() => _BreedingViewState();
}

class _BreedingViewState extends State<BreedingView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  StreamSubscription<dynamic>? _breedingRecordSubscription;
  bool _isLoading = true;

  // Add timestamp tracking for breeding update

  @override
  void initState() {
    super.initState();
    _subscribeToRecordUpdates();
    _loadBreedingData();
  }

  @override
  void dispose() {
    _breedingRecordSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(BreedingView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.cattle.tagId != widget.cattle.tagId) {
      _subscribeToRecordUpdates();
      _loadBreedingData();
    }
  }

  void _subscribeToRecordUpdates() {
    // Cancel existing subscription
    _breedingRecordSubscription?.cancel();

    // Subscribe to breeding records
    _breedingRecordSubscription =
        _databaseHelper.breedingRecordStream.listen((record) async {
      if (mounted) {
        // Get the event details
        final eventCattleId = record['cattleId'] as String?;
        final cattleId = widget.cattle.tagId;

        // Check if this event is relevant to our cattle and both IDs are not null
        if (eventCattleId != null &&
            cattleId != null &&
            eventCattleId == cattleId) {
          setState(() => _isLoading = true);

          try {
            // Always fetch fresh cattle data for any breeding-related event
            final updatedCattle =
                await _databaseHelper.cattleHandler.getCattleByTagId(cattleId);
            if (updatedCattle != null && mounted) {
              // Update the cattle data first
              widget.onCattleUpdated(updatedCattle);

              // Then load breeding data with the latest data
              await _loadBreedingData();

              // Update loading state after all operations are complete
              if (mounted) {
                setState(() => _isLoading = false);
              }
            }
          } catch (e) {
            debugPrint('Error updating breeding view: $e');
            if (mounted) {
              setState(() => _isLoading = false);
            }
          }
        }
      }
    }, onError: (error) {
      debugPrint('Error in breeding record stream: $error');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }

  // Method to load breeding data and refresh eligibility status
  Future<void> _loadBreedingData() async {
    if (!mounted) return;

    final cattleId = widget.cattle.tagId;
    if (cattleId == null) {
      debugPrint('Cannot load breeding data: Cattle ID is missing');
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Fetch the latest cattle data to ensure we have the most up-to-date information
      final updatedCattle =
          await _databaseHelper.cattleHandler.getCattleByTagId(cattleId);
      if (updatedCattle != null && mounted) {
        // Update the cattle data in the parent widget
        widget.onCattleUpdated(updatedCattle);
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading breeding data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Add method to get next breeding number for a cattle
  Future<int> _getNextBreedingNumber(String cattleId) async {
    try {
      final breedingRecords = await _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle(cattleId);
      return breedingRecords.length + 1;
    } catch (e) {
      debugPrint('Error getting next breeding number: $e');
      return 1;
    }
  }

  // Method to create breeding record ID
  String _createBreedingRecordId(String cattleId, int sequenceNumber) {
    return 'BR_${cattleId}_${sequenceNumber.toString().padLeft(3, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadBreedingData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Breeding Stats Card
                    _buildBreedingStatsCard(),

                    const SizedBox(height: 16),

                    // Breeding Eligibility Card
                    _buildBreedingEligibilityCard(),

                    const SizedBox(height: 16),

                    // Breeding History Card
                    _buildBreedingHistoryCard(),

                    // Add padding at the bottom for the FAB
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddBreedingForm(context),
        backgroundColor: const Color(0xFF2E7D32),
        tooltip: 'Add Breeding Record',
        child: const Icon(Icons.add),
      ),
    );
  }

  // Add method to show breeding form
  Future<void> _showAddBreedingForm(BuildContext context) async {
    if (_isLoading) return;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => BreedingFormDialog(
        cattleTagId: widget.cattle.tagId,
      ),
    );

    if (result != null) {
      await _addBreedingRecord(result);
    }
  }

  // Add method to show edit breeding form
  Future<void> _showEditBreedingForm(
      BuildContext context, Map<String, dynamic> record) async {
    if (_isLoading) return;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => BreedingFormDialog(
        record: record,
        cattleTagId: widget.cattle.tagId,
      ),
    );

    if (result != null) {
      await _editBreedingRecord(record['id'], result);
    }
  }

  // Add method to build breeding eligibility card
  Widget _buildBreedingEligibilityCard() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle(widget.cattle.tagId ?? '')
          .then(
            (records) => records.map((record) => record.toMap()).toList(),
          ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        final breedingRecords = snapshot.data ?? [];

        return EligibilityCard.breeding(
          gender: widget.cattle.gender ?? '',
          cattleId: widget.cattle.tagId ?? '',
          animalTypeId: widget.cattle.animalTypeId ?? '',
          isPregnant: widget.cattle.breedingStatus?.isPregnant ?? false,
          dateOfBirth: widget.cattle.dateOfBirth,
          purchaseDate: widget.cattle.purchaseDate,
          lastBreedingDate: widget.cattle.breedingStatus?.breedingDate,
          lastCalvingDate: widget.cattle.breedingStatus?.lastCalvingDate,
          breedingRecords: breedingRecords,
          onAddPressed: () => _showAddBreedingForm(context),
          trailing: IconButton(
            icon: const Icon(Icons.info_outline, color: Color(0xFF2E7D32)),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Breeding Eligibility Information'),
                  content: const Text(
                      'This card shows the breeding eligibility. The system checks for gender, age, pregnancy status, and required waiting periods.'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildBreedingHistoryCard() {
    final cattleId = widget.cattle.tagId;
    if (cattleId == null) {
      return const Card(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('Error: Cattle ID is missing'),
          ),
        ),
      );
    }

    // Add a unique key based on cattle state to force rebuild when state changes
    final keyString =
        'history_${widget.cattle.breedingStatus?.isPregnant}_${widget.cattle.breedingStatus?.breedingDate?.toIso8601String()}_${DateTime.now().millisecondsSinceEpoch}';

    return FutureBuilder<List<Map<String, dynamic>>>(
      key: ValueKey(keyString),
      future: _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle(cattleId)
          .then(
            (records) => records.map((record) => record.toMap()).toList(),
          ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Card(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error loading breeding records: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          );
        }

        final breedingRecords = snapshot.data ?? [];

        // Sort by date, most recent first
        if (breedingRecords.isNotEmpty) {
          breedingRecords.sort((a, b) {
            final dateA = DateTime.parse(a['date']);
            final dateB = DateTime.parse(b['date']);
            return dateB.compareTo(dateA);
          });
        }

        // Use the reusable BreedingHistoryCard widget
        return BreedingHistoryCard(
          records: breedingRecords,
          title: 'Breeding History',
          emptyMessage: 'No breeding history available',
          cattleName: widget.cattle.name,
          cattleId: widget.cattle.tagId,
          onEdit: (record) {
            _showEditBreedingForm(context, record);
          },
          onDelete: (record) {
            _deleteBreedingRecord(record);
          },
          onStatusTap: (record) {
            _showStatusChangeDialog(record);
          },
        );
      },
    );
  }

  // Add this method for status change dialog
  Future<void> _showStatusChangeDialog(Map<String, dynamic> record) async {
    final currentStatus = record['status']?.toString() ?? 'Unknown';
    String selectedStatus = currentStatus;
    final cattleName = widget.cattle.name ?? 'Unknown';
    DateTime.parse(record['date'] as String);
    final cattleId = widget.cattle.tagId;

    if (cattleId == null || cattleId.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error: Cattle ID is missing'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Breeding Status'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Cattle: $cattleName'),
                Text('Tag ID: $cattleId'),
                const SizedBox(height: 16),
                Text('Current Status: $currentStatus'),
                const SizedBox(height: 16),
                const Text('Select new status:'),
                ListTile(
                  title: const Text('Pending'),
                  leading: Radio<String>(
                    value: 'Pending',
                    groupValue: selectedStatus,
                    onChanged: (value) {
                      setState(() => selectedStatus = value!);
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Confirmed'),
                  leading: Radio<String>(
                    value: 'Confirmed',
                    groupValue: selectedStatus,
                    onChanged: (value) {
                      setState(() => selectedStatus = value!);
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Completed'),
                  leading: Radio<String>(
                    value: 'Completed',
                    groupValue: selectedStatus,
                    onChanged: (value) {
                      setState(() => selectedStatus = value!);
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Failed'),
                  leading: Radio<String>(
                    value: 'Failed',
                    groupValue: selectedStatus,
                    onChanged: (value) {
                      setState(() => selectedStatus = value!);
                    },
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(selectedStatus),
            child: const Text('Update'),
          ),
        ],
      ),
    );

    if (result != null && result != currentStatus) {
      try {
        setState(() => _isLoading = true);

        // Create updated record with new status
        final updatedRecordMap = Map<String, dynamic>.from(record);
        updatedRecordMap['status'] = result;

        // Update the record
        await _databaseHelper.breedingHandler
            .updateBreedingRecordFromMap(updatedRecordMap);

        // Reload data to ensure all components are updated
        await _loadBreedingData();

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Breeding status updated to "$result"'),
            backgroundColor: const Color(0xFF2E7D32),
          ),
        );

        setState(() => _isLoading = false);
      } catch (e) {
        if (mounted) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error updating breeding record: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Add method to build breeding stats card
  Widget _buildBreedingStatsCard() {
    final cattleId = widget.cattle.tagId;
    if (cattleId == null) {
      return const Card(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('Error: Cattle ID is missing'),
          ),
        ),
      );
    }

    // Add a unique key based on cattle state to force rebuild when state changes
    final keyString =
        'stats_${widget.cattle.breedingStatus?.isPregnant}_${widget.cattle.breedingStatus?.breedingDate?.toIso8601String()}_${DateTime.now().millisecondsSinceEpoch}';

    return FutureBuilder<List<Map<String, dynamic>>>(
      key: ValueKey(keyString),
      future: _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle(cattleId)
          .then(
            (records) => records.map((record) => record.toMap()).toList(),
          ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Card(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error loading breeding statistics: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          );
        }

        final breedingRecords = snapshot.data ?? [];

        // Calculate statistics from breeding records - using case-insensitive comparison
        final successfulBreedings = breedingRecords
            .where((record) =>
                (record['status']?.toString().toLowerCase() ?? '') ==
                'completed')
            .length;
        final failedBreedings = breedingRecords
            .where((record) =>
                (record['status']?.toString().toLowerCase() ?? '') == 'failed')
            .length;
        final pendingBreedings = breedingRecords
            .where((record) =>
                (record['status']?.toString().toLowerCase() ?? '') == 'pending')
            .length;
        final confirmedBreedings = breedingRecords
            .where((record) =>
                (record['status']?.toString().toLowerCase() ?? '') ==
                'confirmed')
            .length;
        final totalBreedings = breedingRecords.length;

        // Use the factory method for StatsCard
        return StatsCard.breedingStats(
          totalBreedings: totalBreedings,
          pendingBreedings: pendingBreedings,
          confirmedBreedings: confirmedBreedings,
          completedBreedings: successfulBreedings,
          failedBreedings: failedBreedings,
          onViewAllTap: () {
            // Optional: Navigate to detailed breeding records view
          },
          onInfoTap: () {
            // Show breeding stats information
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Breeding Statistics Info'),
                content: const Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                        'These statistics show the breeding history for this cattle:'),
                    SizedBox(height: 8),
                    Text('• Total: All breeding attempts for this cattle'),
                    Text('• Pending: Breeding attempts awaiting confirmation'),
                    Text(
                        '• Confirmed: Confirmed pregnancies awaiting delivery'),
                    Text(
                        '• Completed: Successfully completed breedings with calving'),
                    Text('• Failed: Unsuccessful breeding attempts'),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Close'),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Add breeding record method
  Future<void> _addBreedingRecord(Map<String, dynamic> result) async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    final cattleId = widget.cattle.tagId;
    if (cattleId == null || cattleId.isEmpty) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Error: Cattle ID is missing'),
            backgroundColor: Colors.red),
      );
      return;
    }

    try {
      // Get the next sequence number
      final sequenceNumber = await _getNextBreedingNumber(cattleId);

      // Create ID for the breeding record
      final breedingRecordId =
          _createBreedingRecordId(cattleId, sequenceNumber);

      // Add record ID to the result map
      final recordMap = Map<String, dynamic>.from(result);
      recordMap['id'] = breedingRecordId;

      // Add the record to the database
      await _databaseHelper.breedingHandler.addBreedingRecord(BreedingRecordIsar.fromMap(recordMap));

      // Reload breeding data
      await _loadBreedingData();

      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Breeding record added successfully'),
            backgroundColor: Color(0xFF2E7D32),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error adding breeding record: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding breeding record: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Edit breeding record method
  Future<void> _editBreedingRecord(
      String recordId, Map<String, dynamic> result) async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      // Update the record ID in the result map
      final recordMap = Map<String, dynamic>.from(result);
      recordMap['id'] = recordId;

      // Update the record in the database
      await _databaseHelper.breedingHandler.updateBreedingRecordFromMap(recordMap);

      // Reload breeding data
      await _loadBreedingData();

      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Breeding record updated successfully'),
            backgroundColor: Color(0xFF2E7D32),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error editing breeding record: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error editing breeding record: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Delete breeding record method
  Future<void> _deleteBreedingRecord(Map<String, dynamic> record) async {
    if (!mounted) return;

    // Ask for confirmation
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: Text(
            'Are you sure you want to delete this breeding record from ${widget.cattle.name ?? "this cattle"}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isLoading = true);

    try {
      // Delete the record from the database
      await _databaseHelper.breedingHandler.deleteBreedingRecord(record['id']);

      // Reload breeding data
      await _loadBreedingData();

      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Breeding record deleted successfully'),
            backgroundColor: Color(0xFF2E7D32),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error deleting breeding record: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting breeding record: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method for status colors
}
*/

// END OF LEGACY BREEDING VIEW FILE
