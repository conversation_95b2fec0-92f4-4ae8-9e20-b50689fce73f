import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../models/vaccination_record_isar.dart';
import '../controllers/health_details_controller.dart';
import '../dialogs/vaccination_form_dialog.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/message_utils.dart';


class HealthDetailsVaccineTab extends StatefulWidget {
  const HealthDetailsVaccineTab({Key? key}) : super(key: key);

  @override
  State<HealthDetailsVaccineTab> createState() => _HealthDetailsVaccineTabState();
}

class _HealthDetailsVaccineTabState extends State<HealthDetailsVaccineTab> {

  @override
  Widget build(BuildContext context) {
    return Consumer<HealthDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final vaccinationRecords = controller.vaccinationRecords;

        if (cattle == null) {
          return const Center(child: Text('No cattle data available'));
        }

        if (vaccinationRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Vaccination Records',
            message: 'No vaccination records found for ${cattle.name ?? cattle.tagId}',
            tabColor: AppColors.healthHeader,
            tabIndex: 2, // Vaccine tab (0-based: Analytics=0, Health=1, Vaccines=2)
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80), // Bottom padding for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Records list
              _buildRecordsList(vaccinationRecords),
            ],
          ),
        );
      },
    );
  }

  /// Build records list
  Widget _buildRecordsList(List<VaccinationIsar> vaccinationRecords) {
    return Column(
      children: vaccinationRecords.map((record) => Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: _buildVaccinationRecordCard(record),
      )).toList(),
    );
  }

  Widget _buildVaccinationRecordCard(VaccinationIsar record) {
    // Format row 1: Date (since cattle context is already known in detail view)
    String dateText = record.date != null
        ? DateFormat('MMM dd, yyyy').format(record.date!)
        : 'No date';

    // Format row 2: Vaccine name + Cost
    String vaccineName = record.vaccineName ?? 'Unknown Vaccine';
    String costText = record.cost != null && record.cost! > 0
        ? '\$${record.cost!.toStringAsFixed(2)}'
        : 'No cost';

    // Format row 3: Batch + Manufacturer (conditional display)
    bool hasBatch = record.batchNumber?.isNotEmpty == true;
    bool hasManufacturer = record.manufacturer?.isNotEmpty == true;

    return UniversalRecordCard(
      // Row 1: Date + Status/Type (since cattle context is known)
      row1Left: dateText,
      row1Right: 'Vaccination',
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.vaccines,

      // Row 2: Vaccine Name + Cost (matching main vaccination tab pattern)
      row2Left: vaccineName,
      row2Right: costText,
      row2LeftIcon: Icons.vaccines,
      row2RightIcon: Icons.receipt_long,

      // Row 3: Batch + Manufacturer (conditional display only when data exists)
      row3Left: hasBatch ? 'Batch: ${record.batchNumber!}' : null,
      row3Right: hasManufacturer ? record.manufacturer! : null,
      row3LeftIcon: hasBatch ? Icons.qr_code : null,
      row3RightIcon: hasManufacturer ? Icons.business : null,

      // Notes
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.healthHeader,

      onTap: () => _showRecordDetails(record),
      onEdit: () => _editRecord(record),
      onDelete: () => _deleteRecord(record),
    );
  }

  void _showRecordDetails(VaccinationIsar record) {
    // Show detailed view in a dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Vaccination Record Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Date', record.date != null
                  ? DateFormat('MMMM dd, yyyy').format(record.date!)
                  : 'Not specified'),
              _buildDetailRow('Vaccine', record.vaccineName ?? 'Unknown Vaccine'),
              if (record.cost != null && record.cost! > 0)
                _buildDetailRow('Cost', '\$${record.cost!.toStringAsFixed(2)}'),
              if (record.batchNumber?.isNotEmpty == true)
                _buildDetailRow('Batch Number', record.batchNumber!),
              if (record.manufacturer?.isNotEmpty == true)
                _buildDetailRow('Manufacturer', record.manufacturer!),
              if (record.nextDueDate != null)
                _buildDetailRow('Next Due', DateFormat('MMMM dd, yyyy').format(record.nextDueDate!)),
              if (record.notes?.isNotEmpty == true)
                _buildDetailRow('Notes', record.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editRecord(VaccinationIsar record) {
    final controller = Provider.of<HealthDetailsController>(context, listen: false);

    print('🔧 VACCINATION DETAILS TAB: Opening edit dialog for vaccination ${record.id}');
    showDialog(
      context: context,
      builder: (context) => VaccinationFormDialog(
        cattle: controller.cattle != null ? [controller.cattle!] : [],
        cattleId: controller.cattle?.tagId ?? '',
        vaccination: record,
        onSave: (updatedRecord) async {
          try {
            print('💾 VACCINATION DETAILS TAB: Starting update for vaccination ${updatedRecord.id}');

            await controller.updateVaccinationRecord(updatedRecord);

            print('✅ VACCINATION DETAILS TAB: Update completed for vaccination ${updatedRecord.id}');

            if (mounted) {
              MessageUtils.showSuccess(context, 'Vaccination record updated successfully');
            }
            return true;
          } catch (e) {
            print('❌ VACCINATION DETAILS TAB: Update failed for vaccination ${updatedRecord.id}: $e');
            if (mounted) {
              MessageUtils.showError(context, 'Failed to update vaccination record: $e');
            }
            return false;
          }
        },
      ),
    );
  }

  void _deleteRecord(VaccinationIsar record) {
    final controller = Provider.of<HealthDetailsController>(context, listen: false);
    final cattle = controller.cattle;
    final recordDescription = 'Vaccination record for ${cattle?.name ?? 'this cattle'}';

    print('🗑️ VACCINATION DETAILS TAB: Opening delete confirmation for vaccination ${record.id}');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Vaccination'),
        content: Text('Are you sure you want to delete this $recordDescription?'),
        actions: [
          TextButton(
            onPressed: () {
              print('❌ VACCINATION DETAILS TAB: Delete cancelled for vaccination ${record.id}');
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                print('🗑️ VACCINATION DETAILS TAB: Starting delete for vaccination ${record.id}');

                await controller.deleteVaccinationRecord(record.id);

                print('✅ VACCINATION DETAILS TAB: Delete completed for vaccination ${record.id}');

                if (mounted) {
                  MessageUtils.showSuccess(context, 'Vaccination record deleted successfully');
                }
              } catch (e) {
                print('❌ VACCINATION DETAILS TAB: Delete failed for vaccination ${record.id}: $e');
                if (mounted) {
                  MessageUtils.showError(context, 'Failed to delete vaccination record: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}