import 'package:isar/isar.dart';

import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../../services/database/isar_service.dart';

// Legacy alias for compatibility
typedef HealthHandler = HealthRepository;

/// Pure reactive repository for Health module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class HealthRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  HealthRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE HEALTH STREAMS ===//

  /// Watches all health records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<HealthRecordIsar>> watchAllHealthRecords() {
    print('🔄 HEALTH REPOSITORY: Setting up watchAllHealthRecords stream');
    return _isar.healthRecordIsars.where().watch(fireImmediately: true).map((records) {
      print('🔄 HEALTH REPOSITORY: watchAllHealthRecords stream fired with ${records.length} health records');
      return records;
    });
  }

  /// Watches all medications with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<MedicationIsar>> watchAllMedications() {
    print('🔄 HEALTH REPOSITORY: Setting up watchAllMedications stream');
    return _isar.medicationIsars.where().watch(fireImmediately: true).map((medications) {
      print('🔄 HEALTH REPOSITORY: watchAllMedications stream fired with ${medications.length} medications');
      return medications;
    });
  }

  /// Watches all treatments with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<TreatmentIsar>> watchAllTreatments() {
    print('🔄 HEALTH REPOSITORY: Setting up watchAllTreatments stream');
    return _isar.treatmentIsars.where().watch(fireImmediately: true).map((treatments) {
      print('🔄 HEALTH REPOSITORY: watchAllTreatments stream fired with ${treatments.length} treatments');
      return treatments;
    });
  }

  /// Watches all vaccinations with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<VaccinationIsar>> watchAllVaccinations() {
    print('🔄 HEALTH REPOSITORY: Setting up watchAllVaccinations stream');
    return _isar.vaccinationIsars.where().watch(fireImmediately: true).map((vaccinations) {
      print('🔄 HEALTH REPOSITORY: watchAllVaccinations stream fired with ${vaccinations.length} vaccinations');
      return vaccinations;
    });
  }

  //=== HEALTH RECORDS CRUD ===//

  /// Save (add or update) a health record using Isar's native upsert
  Future<void> saveHealthRecord(HealthRecordIsar record) async {
    print('💾 HEALTH REPOSITORY: Starting saveHealthRecord for ID ${record.id}');

    await _isar.writeTxn(() async {
      final savedId = await _isar.healthRecordIsars.put(record);
      print('✅ HEALTH REPOSITORY: Health record saved with ID $savedId');
    });

    // Check count after save
    final countAfterSave = await _isar.healthRecordIsars.count();
    print('📊 HEALTH REPOSITORY: Total health records count after save: $countAfterSave');
    print('✅ HEALTH REPOSITORY: saveHealthRecord completed');
  }

  /// Delete a health record by its Isar ID
  Future<void> deleteHealthRecord(int id) async {
    print('🗑️ HEALTH REPOSITORY: Starting deleteHealthRecord for ID $id');

    // Check if record exists before delete
    final existingRecord = await _isar.healthRecordIsars.get(id);
    print('📋 HEALTH REPOSITORY: Health record exists before delete: ${existingRecord != null}');

    await _isar.writeTxn(() async {
      final deleteResult = await _isar.healthRecordIsars.delete(id);
      print('✅ HEALTH REPOSITORY: Delete operation result: $deleteResult (true = deleted, false = not found)');
    });

    // Check count after delete
    final countAfterDelete = await _isar.healthRecordIsars.count();
    print('📊 HEALTH REPOSITORY: Total health records count after delete: $countAfterDelete');
    print('✅ HEALTH REPOSITORY: deleteHealthRecord completed for ID $id');
  }

  //=== MEDICATION CRUD ===//

  /// Save (add or update) a medication using Isar's native upsert
  Future<void> saveMedication(MedicationIsar medication) async {
    await _isar.writeTxn(() async {
      await _isar.medicationIsars.put(medication);
    });
  }

  /// Delete a medication by its Isar ID
  Future<void> deleteMedication(int id) async {
    await _isar.writeTxn(() async {
      await _isar.medicationIsars.delete(id);
    });
  }

  //=== TREATMENT CRUD ===//

  /// Save (add or update) a treatment using Isar's native upsert
  Future<void> saveTreatment(TreatmentIsar treatment) async {
    await _isar.writeTxn(() async {
      await _isar.treatmentIsars.put(treatment);
    });
  }

  /// Delete a treatment by its Isar ID
  Future<void> deleteTreatment(int id) async {
    await _isar.writeTxn(() async {
      await _isar.treatmentIsars.delete(id);
    });
  }

  //=== VACCINATION CRUD ===//

  /// Save (add or update) a vaccination using Isar's native upsert
  Future<void> saveVaccination(VaccinationIsar vaccination) async {
    print('💾 HEALTH REPOSITORY: Starting saveVaccination for ID ${vaccination.id}');

    await _isar.writeTxn(() async {
      final savedId = await _isar.vaccinationIsars.put(vaccination);
      print('✅ HEALTH REPOSITORY: Vaccination saved with ID $savedId');
    });

    // Check count after save
    final countAfterSave = await _isar.vaccinationIsars.count();
    print('📊 HEALTH REPOSITORY: Total vaccinations count after save: $countAfterSave');
    print('✅ HEALTH REPOSITORY: saveVaccination completed');
  }

  /// Delete a vaccination by its Isar ID
  Future<void> deleteVaccination(int id) async {
    print('🗑️ HEALTH REPOSITORY: Starting deleteVaccination for ID $id');

    // Check if record exists before delete
    final existingRecord = await _isar.vaccinationIsars.get(id);
    print('📋 HEALTH REPOSITORY: Record exists before delete: ${existingRecord != null}');

    await _isar.writeTxn(() async {
      final deleteResult = await _isar.vaccinationIsars.delete(id);
      print('✅ HEALTH REPOSITORY: Delete operation result: $deleteResult (true = deleted, false = not found)');
    });

    // Check count after delete
    final countAfterDelete = await _isar.vaccinationIsars.count();
    print('📊 HEALTH REPOSITORY: Total vaccinations count after delete: $countAfterDelete');
    print('✅ HEALTH REPOSITORY: deleteVaccination completed for ID $id');
  }
}


