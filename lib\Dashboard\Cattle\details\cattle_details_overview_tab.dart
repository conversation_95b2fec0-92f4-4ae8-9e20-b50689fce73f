import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../controllers/cattle_details_controller.dart';
import '../utils/cattle_age_calculator.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../widgets/index.dart';
import '../../../utils/message_utils.dart';

/// Data class for info cards to provide type safety
class InfoCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;

  const InfoCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}

class CattleDetailsOverviewTab extends StatelessWidget {
  const CattleDetailsOverviewTab({super.key});

  @override
  Widget build(BuildContext context) {

    return Consumer<CattleDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final breed = controller.currentBreed;
        final animalType = controller.getAnimalType(cattle?.animalTypeId);

        if (cattle == null) {
          return const Center(child: Text('No cattle data available'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(kPaddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPictureSection(cattle),
              const SizedBox(height: kSpacingLarge),
              _buildBasicInformation(cattle, breed, animalType),
              const SizedBox(height: kSpacingLarge),
              _buildPhysicalCharacteristics(cattle),
              const SizedBox(height: kSpacingLarge),
              _buildHealthAndBreeding(cattle),
              const SizedBox(height: kSpacingLarge),
              _buildSourceAndFinancial(cattle),
            ],
          ),
        );
      },
    );
  }



  Widget _buildBasicInformation(CattleIsar cattle, BreedCategoryIsar? breed, AnimalTypeIsar? animalType) {
    final cardData = _buildBasicInformationCards(cattle, breed, animalType);
    return _buildGridSection(
      title: 'Basic Information',
      subtitle: 'Essential details for ${cattle.name ?? 'this cattle'}',
      icon: Icons.info_outlined,
      headerColor: AppColors.cattleKpiSection,
      cardData: cardData,
    );
  }

  /// Build basic information cards with type safety
  List<InfoCardData> _buildBasicInformationCards(CattleIsar cattle, BreedCategoryIsar? breed, AnimalTypeIsar? animalType) {
    return [
      InfoCardData(
        title: 'Name (Tag ID)',
        value: '${cattle.name ?? 'Unnamed'} (${cattle.tagId ?? 'N/A'})',
        subtitle: 'Identification',
        icon: Icons.badge,
        color: AppColors.cattleKpiColors[0],
      ),
      InfoCardData(
        title: 'Gender',
        value: cattle.gender.displayName,
        subtitle: 'Biological Sex',
        icon: cattle.gender.name.toLowerCase() == 'male' ? Icons.male : Icons.female,
        color: AppColors.cattleKpiColors[1],
      ),
      InfoCardData(
        title: 'Animal Type',
        value: animalType?.name ?? 'Unknown',
        subtitle: 'Species',
        icon: Icons.pets,
        color: AppColors.cattleKpiColors[2],
      ),
      InfoCardData(
        title: 'Breed',
        value: breed?.name ?? 'Unknown',
        subtitle: 'Breed Category',
        icon: Icons.category,
        color: AppColors.cattleKpiColors[3],
      ),
      InfoCardData(
        title: 'Age',
        value: CattleAgeCalculator.calculateAgeDisplay(cattle),
        subtitle: 'Current Age',
        icon: Icons.cake,
        color: AppColors.cattleKpiColors[4],
      ),
      InfoCardData(
        title: 'Birth Date',
        value: cattle.formattedBirthDate,
        subtitle: 'Date of Birth',
        icon: Icons.event,
        color: AppColors.cattleKpiColors[5],
      ),
    ];
  }

  Widget _buildPhysicalCharacteristics(CattleIsar cattle) {
    final cardData = _buildPhysicalCharacteristicsCards(cattle);
    return _buildGridSection(
      title: 'Physical Characteristics',
      subtitle: 'Physical attributes and measurements',
      icon: Icons.monitor_weight,
      headerColor: AppColors.cattleAgeDemographics,
      cardData: cardData,
    );
  }

  /// Build physical characteristics cards with type safety
  List<InfoCardData> _buildPhysicalCharacteristicsCards(CattleIsar cattle) {
    return [
      InfoCardData(
        title: 'Weight',
        value: cattle.weight != null ? '${cattle.weight} kg' : 'Not recorded',
        subtitle: 'Body weight',
        icon: Icons.monitor_weight,
        color: AppColors.cattleKpiColors[0],
      ),
      InfoCardData(
        title: 'Color',
        value: cattle.color?.isNotEmpty == true ? cattle.color! : 'Not recorded',
        subtitle: 'Skin color',
        icon: Icons.palette,
        color: AppColors.cattleKpiColors[1],
      ),
    ];
  }

  Widget _buildHealthAndBreeding(CattleIsar cattle) {
    final cardData = _buildHealthAndBreedingCards(cattle);
    return _buildGridSection(
      title: 'Health & Breeding',
      subtitle: 'Health status and breeding information',
      icon: Icons.favorite,
      headerColor: AppColors.cattleFinancialOverview,
      cardData: cardData,
    );
  }

  /// Build health and breeding cards with type safety
  List<InfoCardData> _buildHealthAndBreedingCards(CattleIsar cattle) {
    final cards = <InfoCardData>[
      InfoCardData(
        title: 'Status',
        value: cattle.status.displayName,
        subtitle: 'Current Status',
        icon: Icons.health_and_safety,
        color: AppColors.cattleKpiColors[0],
      ),
    ];

    if (cattle.gender.name.toLowerCase() == 'female') {
      cards.add(InfoCardData(
        title: 'Heat Status',
        value: 'Not in heat', // Placeholder
        subtitle: 'Estrus Cycle',
        icon: Icons.favorite,
        color: AppColors.cattleKpiColors[1],
      ));
    }

    return cards;
  }

  Widget _buildSourceAndFinancial(CattleIsar cattle) {
    final cardData = _buildSourceAndFinancialCards(cattle);
    return _buildGridSection(
      title: 'Source & Financial',
      subtitle: 'Origin and investment details',
      icon: Icons.attach_money,
      headerColor: AppColors.cattleHerdComposition,
      cardData: cardData,
    );
  }

  /// Build source and financial cards with type safety
  List<InfoCardData> _buildSourceAndFinancialCards(CattleIsar cattle) {
    return [
      InfoCardData(
        title: 'Source',
        value: cattle.source.displayName,
        subtitle: 'Origin',
        icon: Icons.location_on,
        color: AppColors.cattleKpiColors[0],
      ),
      InfoCardData(
        title: 'Purchase Price',
        value: _formatCurrency(cattle.purchasePrice),
        subtitle: 'Initial Cost',
        icon: Icons.shopping_cart,
        color: AppColors.cattleKpiColors[1],
      ),
      InfoCardData(
        title: 'Purchase Date',
        value: cattle.formattedPurchaseDate,
        subtitle: 'Acquisition Date',
        icon: Icons.calendar_today,
        color: AppColors.cattleKpiColors[2],
      ),
      InfoCardData(
        title: 'Days Owned',
        value: cattle.formattedDaysOwned,
        subtitle: 'Ownership Period',
        icon: Icons.schedule,
        color: AppColors.cattleKpiColors[3],
      ),
    ];
  }

  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<InfoCardData> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header using universal component
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),

        // 2. Use ResponsiveGrid.cards for consistent responsive behavior
        ResponsiveGrid.cards(
          children: cardData.map((data) {
            return UniversalInfoCard(
              title: data.title,
              value: data.value,
              subtitle: data.subtitle,
              icon: data.icon,
              color: data.color,
            );
          }).toList(),
        ),
      ],
    );
  }

  // Helper methods for presentation logic only
  String _formatCurrency(double? amount) {
    if (amount == null) return 'N/A';
    return NumberFormat.currency(locale: 'en_US', symbol: '\$').format(amount);
  }

  /// Build picture section with image display and edit functionality
  Widget _buildPictureSection(CattleIsar cattle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Picture',
          icon: Icons.photo_camera,
          color: AppColors.cattleKpiColors[5], // Deep Purple - different from blue
          subtitle: 'Photo of ${cattle.name ?? 'this cattle'}',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        _buildPictureCard(cattle),
      ],
    );
  }

  /// Build picture card with image display and edit functionality
  Widget _buildPictureCard(CattleIsar cattle) {
    return Builder(
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(kBorderRadius * 2),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Image display area
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(kBorderRadius * 2),
                  topRight: Radius.circular(kBorderRadius * 2),
                ),
                color: Colors.grey[100],
              ),
              child: _buildImageDisplay(cattle),
            ),
            // Action buttons
            Container(
              padding: const EdgeInsets.all(kPaddingMedium),
              child: cattle.photoPath != null
                ? Row(
                    children: [
                      // Change Photo button - takes half space
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _pickImage(cattle, context),
                          icon: const Icon(Icons.camera_alt),
                          label: const Text('Change Photo'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(kBorderRadius),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: kSpacingMedium),
                      // Remove button - takes half space
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _removeImage(cattle, context),
                          icon: const Icon(Icons.delete_outline),
                          label: const Text('Remove'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.error,
                            side: const BorderSide(color: AppColors.error),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(kBorderRadius),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _pickImage(cattle, context),
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('Add Photo'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(kBorderRadius),
                        ),
                      ),
                    ),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build image display widget
  Widget _buildImageDisplay(CattleIsar cattle) {
    if (cattle.photoPath != null && cattle.photoPath!.isNotEmpty) {
      final imageFile = File(cattle.photoPath!);
      if (imageFile.existsSync()) {
        return Builder(
          builder: (context) => InkWell(
            onTap: () => _showFullImageDialog(context, imageFile),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(kBorderRadius * 2),
              topRight: Radius.circular(kBorderRadius * 2),
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(kBorderRadius * 2),
                    topRight: Radius.circular(kBorderRadius * 2),
                  ),
                  child: Image.file(
                    imageFile,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildPlaceholder(cattle);
                    },
                  ),
                ),
                // Overlay with view icon
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(kBorderRadius),
                    ),
                    child: const Icon(
                      Icons.fullscreen,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }
    return _buildPlaceholder(cattle);
  }

  /// Build placeholder when no image is available
  Widget _buildPlaceholder(CattleIsar cattle) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(kBorderRadius * 2),
          topRight: Radius.circular(kBorderRadius * 2),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_camera_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: kSpacingSmall),
          Text(
            'No photo available',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          const SizedBox(height: kSpacingSmall),
          Text(
            'Tap "Add Photo" to upload an image',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// Pick image from gallery or camera
  Future<void> _pickImage(CattleIsar cattle, BuildContext context) async {
    try {

      final ImagePicker picker = ImagePicker();

      // Show options for camera or gallery
      final ImageSource? source = await showDialog<ImageSource>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select Image Source'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Camera'),
                  onTap: () => Navigator.of(context).pop(ImageSource.camera),
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Gallery'),
                  onTap: () => Navigator.of(context).pop(ImageSource.gallery),
                ),
              ],
            ),
          );
        },
      );

      if (source == null) return;

      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image == null) return;

      // Get the app's documents directory
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'cattle_${cattle.businessId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final savedImage = File('${directory.path}/$fileName');

      // Copy the picked image to the app's documents directory
      await File(image.path).copy(savedImage.path);

      // Update the cattle record with the new photo path
      if (context.mounted) {
        await _updateCattlePhoto(cattle, savedImage.path, context);
      }

    } catch (e) {
      debugPrint('Error picking image: $e');
      if (context.mounted) {
        MessageUtils.showError(context, 'Failed to pick image: $e');
      }
    }
  }

  /// Remove image from cattle
  Future<void> _removeImage(CattleIsar cattle, BuildContext context) async {
    try {
      // Delete the image file if it exists
      if (cattle.photoPath != null && cattle.photoPath!.isNotEmpty) {
        final imageFile = File(cattle.photoPath!);
        if (imageFile.existsSync()) {
          await imageFile.delete();
        }
      }

      // Update the cattle record to remove the photo path
      if (context.mounted) {
        await _updateCattlePhoto(cattle, null, context);
      }

    } catch (e) {
      debugPrint('Error removing image: $e');
      if (context.mounted) {
        MessageUtils.showError(context, 'Failed to remove image: $e');
      }
    }
  }

  /// Update cattle photo path in the database
  Future<void> _updateCattlePhoto(CattleIsar cattle, String? photoPath, BuildContext context) async {
    try {
      final controller = Provider.of<CattleDetailsController>(context, listen: false);

      // Create updated cattle record
      final updatedCattle = CattleIsar()
        ..id = cattle.id
        ..businessId = cattle.businessId
        ..tagId = cattle.tagId
        ..name = cattle.name
        ..animalTypeId = cattle.animalTypeId
        ..breedId = cattle.breedId
        ..gender = cattle.gender
        ..source = cattle.source
        ..motherTagId = cattle.motherTagId
        ..motherBusinessId = cattle.motherBusinessId
        ..dateOfBirth = cattle.dateOfBirth
        ..purchaseDate = cattle.purchaseDate
        ..purchasePrice = cattle.purchasePrice
        ..weight = cattle.weight
        ..color = cattle.color
        ..notes = cattle.notes
        ..photoPath = photoPath // Update the photo path
        ..createdAt = cattle.createdAt
        ..updatedAt = DateTime.now()
        ..category = cattle.category
        ..status = cattle.status
        ..stage = cattle.stage
        ..breedingStatus = cattle.breedingStatus
        ..birthDetails = cattle.birthDetails
        ..healthInfo = cattle.healthInfo
        ..productionInfo = cattle.productionInfo
        ..iconCodePoint = cattle.iconCodePoint
        ..iconFontFamily = cattle.iconFontFamily;

      // Update through the controller
      await controller.updateCattle(updatedCattle);

      if (context.mounted) {
        MessageUtils.showSuccess(context, photoPath != null ? 'Photo updated successfully' : 'Photo removed successfully');
      }

    } catch (e) {
      debugPrint('Error updating cattle photo: $e');
      if (context.mounted) {
        MessageUtils.showError(context, 'Failed to update photo: $e');
      }
    }
  }

  /// Show full image dialog
  void _showFullImageDialog(BuildContext context, File imageFile) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(kPaddingMedium),
          child: Stack(
            children: [
              // Full image
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(kBorderRadius * 2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(kBorderRadius * 2),
                  child: Image.file(
                    imageFile,
                    fit: BoxFit.contain,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
              ),
              // Close button
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(kBorderRadius * 2),
                  ),
                  child: IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

}
