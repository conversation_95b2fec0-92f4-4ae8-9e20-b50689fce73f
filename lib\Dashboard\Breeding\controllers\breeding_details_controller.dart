import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import 'package:isar/isar.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../models/delivery_record_isar.dart';
import '../services/breeding_repository.dart';

/// Controller for managing breeding details screen state and data
class BreedingDetailsController extends ChangeNotifier {
  final BreedingRepository _breedingRepository = GetIt.instance<BreedingRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  bool _isLoading = false;
  String? _error;
  CattleIsar? _cattle;

  // Breeding data
  List<BreedingRecordIsar> _breedingRecords = [];
  List<PregnancyRecordIsar> _pregnancyRecords = [];
  List<DeliveryRecordIsar> _deliveryRecords = [];

  // Real-time synchronization using individual streams (following main breeding controller pattern)
  StreamSubscription<List<BreedingRecordIsar>>? _breedingStreamSubscription;
  StreamSubscription<List<PregnancyRecordIsar>>? _pregnancyStreamSubscription;
  StreamSubscription<List<DeliveryRecordIsar>>? _deliveryStreamSubscription;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  CattleIsar? get cattle => _cattle;
  List<BreedingRecordIsar> get breedingRecords => _breedingRecords;
  List<PregnancyRecordIsar> get pregnancyRecords => _pregnancyRecords;
  List<DeliveryRecordIsar> get deliveryRecords => _deliveryRecords;

  // Computed properties for analytics
  int get totalBreedingAttempts => _breedingRecords.length;
  int get totalPregnancies => _pregnancyRecords.length;
  int get totalDeliveries => _deliveryRecords.length;
  int get successfulPregnancies => _pregnancyRecords.where((p) => p.status == 'Completed').length;

  // Enhanced breeding statistics (from legacy implementation)
  int get pendingBreedings => _breedingRecords.where((r) => r.status?.toLowerCase() == 'pending').length;
  int get confirmedBreedings => _breedingRecords.where((r) => r.status?.toLowerCase() == 'confirmed').length;
  int get completedBreedings => _breedingRecords.where((r) => r.status?.toLowerCase() == 'completed').length;
  int get failedBreedings => _breedingRecords.where((r) => r.status?.toLowerCase() == 'failed').length;

  // Enhanced pregnancy statistics
  int get confirmedPregnancies => _pregnancyRecords.where((p) => p.status?.toLowerCase() == 'confirmed').length;
  int get abortedPregnancies => _pregnancyRecords.where((p) => p.status?.toLowerCase() == 'abortion').length;

  // Enhanced delivery statistics (when delivery records are implemented)
  int get totalCalves => _deliveryRecords.fold(0, (sum, d) => sum + d.calfCount);
  int get successfulDeliveries => _deliveryRecords.where((d) => !d.hadComplications).length;

  double get conceptionRate {
    if (totalBreedingAttempts == 0) return 0.0;
    return (successfulPregnancies / totalBreedingAttempts) * 100;
  }

  bool get isCurrentlyPregnant {
    return _pregnancyRecords.any((p) => p.status == 'Confirmed');
  }

  /// Initialize controller with cattle data
  void initialize(CattleIsar cattle) {
    _cattle = cattle;
    _initializeStreamListeners();
    // No need for manual data loading - streams will handle it automatically
  }

  /// Initialize real-time stream listeners following main breeding controller pattern
  /// Using individual streams instead of StreamZip for better reliability
  void _initializeStreamListeners() {
    if (_cattle == null) {
      debugPrint('Cannot initialize stream listeners: cattle is null');
      return;
    }

    debugPrint('🔧 BREEDING DETAILS: Initializing stream listeners for cattle: ${_cattle!.tagId} (businessId: ${_cattle!.businessId})');

    // Breeding records stream - filtered by cattle tagId (breeding records use tagId in cattleId field)
    _breedingStreamSubscription = _isar.breedingRecordIsars
        .filter()
        .cattleIdEqualTo(_cattle!.tagId)
        .watch(fireImmediately: true)
        .listen((breedingRecords) {
      debugPrint('🔄 BREEDING DETAILS - BREEDING STREAM: Received ${breedingRecords.length} breeding records for cattle ${_cattle!.tagId}');
      _breedingRecords = breedingRecords;
      _setLoading(false);
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Breeding details - breeding stream error: $error');
    });

    // Pregnancy records stream - filtered by cattle tagId (pregnancy records use tagId in cattleId field)
    _pregnancyStreamSubscription = _isar.pregnancyRecordIsars
        .filter()
        .cattleIdEqualTo(_cattle!.tagId)
        .watch(fireImmediately: true)
        .listen((pregnancyRecords) {
      debugPrint('🔄 BREEDING DETAILS - PREGNANCY STREAM: Received ${pregnancyRecords.length} pregnancy records for cattle ${_cattle!.tagId}');
      _pregnancyRecords = pregnancyRecords;
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Breeding details - pregnancy stream error: $error');
    });

    // Delivery records stream - filtered by cattle tagId (delivery records use tagId in cattleId field)
    _deliveryStreamSubscription = _isar.deliveryRecordIsars
        .filter()
        .cattleIdEqualTo(_cattle!.tagId)
        .watch(fireImmediately: true)
        .listen((deliveryRecords) {
      debugPrint('🔄 BREEDING DETAILS - DELIVERY STREAM: Received ${deliveryRecords.length} delivery records for cattle ${_cattle!.tagId}');
      _deliveryRecords = deliveryRecords;
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Breeding details - delivery stream error: $error');
    });

    debugPrint('✅ BREEDING DETAILS: Stream listeners initialized successfully');
  }





  /// Refresh all data - streams handle data loading automatically
  Future<void> refresh() async {
    // No manual refresh needed - streams handle real-time updates
    debugPrint('🔄 BREEDING DETAILS: Refresh called - streams handle updates automatically');
  }

  // CRUD Methods - Stream-Only Pattern
  // These methods only update the database, stream handles UI updates automatically
  // Following the cattle/milk module pattern for real-time synchronization

  /// Add a new breeding record - stream handles UI update automatically
  Future<bool> addBreedingRecord(BreedingRecordIsar record) async {
    try {
      await _breedingRepository.saveBreedingRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to add breeding record: $e');
      return false;
    }
  }

  /// Update an existing breeding record - stream handles UI update automatically
  Future<bool> updateBreedingRecord(BreedingRecordIsar record) async {
    try {
      await _breedingRepository.saveBreedingRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to update breeding record: $e');
      return false;
    }
  }

  /// Delete a breeding record - stream handles UI update automatically
  Future<bool> deleteBreedingRecord(String businessId) async {
    try {
      // Find the breeding record by businessId first, then delete by Isar ID
      final record = _breedingRecords.firstWhere(
        (r) => r.businessId == businessId,
        orElse: () => throw Exception('Breeding record not found'),
      );
      await _breedingRepository.deleteBreedingRecord(record.id);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to delete breeding record: $e');
      return false;
    }
  }

  /// Add a new pregnancy record - stream handles UI update automatically
  Future<bool> addPregnancyRecord(PregnancyRecordIsar record) async {
    try {
      await _breedingRepository.savePregnancyRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to add pregnancy record: $e');
      return false;
    }
  }

  /// Update an existing pregnancy record - stream handles UI update automatically
  Future<bool> updatePregnancyRecord(PregnancyRecordIsar record) async {
    try {
      await _breedingRepository.savePregnancyRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to update pregnancy record: $e');
      return false;
    }
  }

  /// Delete a pregnancy record - stream handles UI update automatically
  Future<bool> deletePregnancyRecord(String businessId) async {
    try {
      // Find the pregnancy record by businessId first, then delete by Isar ID
      final record = _pregnancyRecords.firstWhere(
        (r) => r.businessId == businessId,
        orElse: () => throw Exception('Pregnancy record not found'),
      );
      await _breedingRepository.deletePregnancyRecord(record.id);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to delete pregnancy record: $e');
      return false;
    }
  }

  /// Add a new delivery record - stream handles UI update automatically
  Future<bool> addDeliveryRecord(DeliveryRecordIsar record) async {
    try {
      await _breedingRepository.saveDeliveryRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to add delivery record: $e');
      return false;
    }
  }

  /// Update an existing delivery record - stream handles UI update automatically
  Future<bool> updateDeliveryRecord(DeliveryRecordIsar record) async {
    try {
      await _breedingRepository.saveDeliveryRecord(record);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to update delivery record: $e');
      return false;
    }
  }

  /// Delete a delivery record - stream handles UI update automatically
  Future<bool> deleteDeliveryRecord(String businessId) async {
    try {
      // Find the delivery record by businessId first, then delete by Isar ID
      final record = _deliveryRecords.firstWhere(
        (r) => r.businessId == businessId,
        orElse: () => throw Exception('Delivery record not found'),
      );
      await _breedingRepository.deleteDeliveryRecord(record.id);
      // Stream will handle the UI update automatically
      return true;
    } catch (e) {
      _setError('Failed to delete delivery record: $e');
      return false;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }



  @override
  void dispose() {
    // Clean up all stream subscriptions - following main breeding controller pattern
    _breedingStreamSubscription?.cancel();
    _pregnancyStreamSubscription?.cancel();
    _deliveryStreamSubscription?.cancel();
    super.dispose();
  }
}
