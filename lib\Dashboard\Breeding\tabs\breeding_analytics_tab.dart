import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/breeding_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_tabs.dart';

/// Data class for info cards to replace repetitive Map<String, dynamic> usage
class InfoCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String insight;

  const InfoCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.insight,
  });

  /// Convert to Map for backward compatibility with existing grid builder
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'value': value,
      'subtitle': subtitle,
      'icon': icon,
      'color': color,
      'insight': insight,
    };
  }
}

class BreedingAnalyticsTab extends StatefulWidget {
  final BreedingController controller;

  const BreedingAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<BreedingAnalyticsTab> createState() => _BreedingAnalyticsTabState();
}

class _BreedingAnalyticsTabState extends State<BreedingAnalyticsTab> {
  int _selectedChartIndex = 0; // 0: Breeding Status, 1: Pregnancy Status

  // Use global constants instead of local ones
  static const _fallbackColor = AppColors.fallback;

  // Common chart configuration
  static const _chartRadius = 80.0;
  static const _chartCenterSpace = 50.0;
  static const _chartSectionsSpace = 2.0;
  static const _chartHeight = 240.0;

  // Common text styles
  static const _chartTitleStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  @override
  void initState() {
    super.initState();
    // Data loading is now handled by the controller
  }

  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<Map<String, dynamic>> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header using universal component
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),

        // 2. Use ResponsiveGrid.cards for consistent responsive behavior
        ResponsiveGrid.cards(
          children: cardData.map((data) {
            return UniversalInfoCard(
              title: data['title'] as String,
              value: data['value'] as String,
              subtitle: data['subtitle'] as String?,
              icon: data['icon'] as IconData,
              color: data['color'] as Color,
              badge: data['badge'] as String?,
              insight: data['insight'] as String?,
            );
          }).toList(),
        ),
      ],
    );
  }

  // Universal pie chart builder - pure UI component without business logic
  Widget _buildUniversalPieChart(Map<String, int> data, Map<String, Color> colors, {String? emptyMessage}) {
    if (data.isEmpty || widget.controller.totalBreedingRecords == 0) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    // Ensure we have valid data before rendering
    final validEntries = data.entries.where((entry) => entry.value > 0).toList();
    if (validEntries.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    final sections = validEntries.map((entry) {
      final percentage = (entry.value / widget.controller.totalBreedingRecords) * 100;
      final sectionColor = colors[entry.key] ?? _fallbackColor;

      return PieChartSectionData(
        color: sectionColor,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: _chartRadius,
        titleStyle: _chartTitleStyle,
      );
    }).toList();

    // Add a small delay to ensure smooth rendering
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: _chartCenterSpace,
          sectionsSpace: _chartSectionsSpace,
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Check if we have data to display
        if (widget.controller.totalBreedingRecords == 0) {
          return UniversalTabEmptyState.forTab(
            title: 'No Breeding Data',
            message: 'Add breeding records to your herd to view comprehensive analytics and insights.',
            tabColor: AppColors.breedingHeader,
            tabIndex: 0, // Analytics tab
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () {
                // Navigate to add breeding record screen
                Navigator.of(context).pushNamed('/breeding/add');
              },
              tabColor: AppColors.breedingHeader,
            ),
          );
        }

    // Define sections for clean, declarative layout
    final sections = [
      _buildEnhancedKPIDashboard(context),
      _buildBreedingStatusAnalytics(context),
      _buildPregnancyAnalytics(context),
      _buildBreedingOverview(context),
    ];

    // Use RefreshIndicator with SingleChildScrollView for pull-to-refresh functionality
    return RefreshIndicator(
      onRefresh: () async {
        await widget.controller.refresh();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(kPaddingMedium), // Use global constant
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (int i = 0; i < sections.length; i++) ...[
              sections[i],
              if (i < sections.length - 1) const SizedBox(height: kSpacingLarge), // Use global constant
            ],
          ],
        ),
      ),
    );
      },
    );
  }

  Widget _buildEnhancedKPIDashboard(BuildContext context) {
    final kpiColors = _getKPIColors();
    final kpiCards = _buildKPICards(kpiColors);

    return _buildGridSection(
      title: 'Key Performance Indicators',
      subtitle: 'Essential metrics for your breeding program',
      icon: Icons.dashboard_outlined,
      headerColor: AppColors.breedingHeader,
      cardData: kpiCards.map((card) => card.toMap()..['badge'] = null).toList(),
    );
  }

  /// Build KPI cards using InfoCardData for better type safety
  List<InfoCardData> _buildKPICards(List<Color> kpiColors) {
    return [
      InfoCardData(
        title: 'Total Breeding',
        value: widget.controller.totalBreedingRecords.toString(),
        subtitle: 'breeding records',
        icon: Icons.favorite,
        color: kpiColors[0],
        insight: 'Total breeding attempts',
      ),
      InfoCardData(
        title: 'Pregnancies',
        value: widget.controller.totalPregnancyRecords.toString(),
        subtitle: 'pregnancy records',
        icon: Icons.pregnant_woman,
        color: kpiColors[1],
        insight: 'Current pregnancies',
      ),
      InfoCardData(
        title: 'Female Cattle',
        value: widget.controller.femaleCattle.toString(),
        subtitle: 'breeding ready',
        icon: Icons.female,
        color: kpiColors[2],
        insight: 'Available for breeding',
      ),
      InfoCardData(
        title: 'Active Pregnancies',
        value: widget.controller.activePregnancies.toString(),
        subtitle: 'ongoing',
        icon: Icons.schedule,
        color: kpiColors[3],
        insight: 'Currently pregnant',
      ),
      InfoCardData(
        title: 'Success Rate',
        value: '${widget.controller.conceptionRate.toStringAsFixed(1)}%',
        subtitle: 'breeding success',
        icon: Icons.check_circle,
        color: kpiColors[4],
        insight: 'Breeding efficiency',
      ),
      InfoCardData(
        title: 'Completed',
        value: widget.controller.completedPregnancies.toString(),
        subtitle: 'deliveries',
        icon: Icons.child_care,
        color: kpiColors[5],
        insight: 'Successful births',
      ),
    ];
  }

  Widget _buildBreedingStatusAnalytics(BuildContext context) {
    final statusData = widget.controller.breedingByStatus;
    final pregnancyData = widget.controller.pregnancyByStatus;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Breeding Status Analytics',
          icon: Icons.pie_chart_outline,
          color: AppColors.breedingHeader,
          subtitle: 'Detailed breakdown of breeding status and progress',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),

        // Toggle buttons for chart selection
        _buildChartToggleButtons(context),
        const SizedBox(height: kSpacingMedium),

        // Single chart display based on selection
        _buildSelectedChart(context, statusData, pregnancyData),
      ],
    );
  }

  Widget _buildChartToggleButtons(BuildContext context) {
    final chartOptions = [
      {'title': 'Breeding Status', 'icon': Icons.favorite},
      {'title': 'Pregnancy Status', 'icon': Icons.pregnant_woman},
    ];

    // Use colors from AppColors instead of hardcoded values
    final toggleColors = [
      AppColors.breedingKpiColors[0], // Purple
      AppColors.breedingKpiColors[1], // Green
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chartOptions.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedChartIndex == index;

        return ChoiceChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected ? Colors.white : toggleColors[index],
              ),
              const SizedBox(width: 6),
              Text(
                option['title'] as String,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: isSelected ? Colors.white : toggleColors[index],
                ),
              ),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedChartIndex = index;
              });
            }
          },
          selectedColor: toggleColors[index],
          backgroundColor: Colors.white,
          side: BorderSide(
            color: toggleColors[index],
            width: 2,
          ),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        );
      }).toList(),
    );
  }

  Widget _buildSelectedChart(
    BuildContext context,
    Map<String, int> breedingData,
    Map<String, int> pregnancyData,
  ) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildEnhancedChart(
          context,
          'Breeding Status Distribution',
          _buildUniversalPieChart(breedingData, _getBreedingStatusColors()),
          _buildEnhancedLegend(breedingData, _getBreedingStatusColors()),
          Icons.favorite,
        );
      case 1:
        return _buildEnhancedChart(
          context,
          'Pregnancy Status Distribution',
          _buildUniversalPieChart(pregnancyData, _getPregnancyStatusColors()),
          _buildEnhancedLegend(pregnancyData, _getPregnancyStatusColors()),
          Icons.pregnant_woman,
        );
      default:
        return _buildEnhancedChart(
          context,
          'Breeding Status Distribution',
          _buildUniversalPieChart(breedingData, _getBreedingStatusColors()),
          _buildEnhancedLegend(breedingData, _getBreedingStatusColors()),
          Icons.favorite,
        );
    }
  }

  Widget _buildEnhancedChart(
    BuildContext context,
    String title,
    Widget chart,
    Widget? legend,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(kBorderRadius * 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.breedingHeader.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.breedingHeader,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingLarge),
            SizedBox(height: _chartHeight, child: Center(child: chart)),
            if (legend != null) ...[
              const SizedBox(height: kSpacingMedium),
              legend,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPregnancyAnalytics(BuildContext context) {
    final colors = _getInsightColors();
    final pregnancyCards = _buildPregnancyCards(colors);

    return _buildGridSection(
      title: 'Pregnancy Analytics',
      subtitle: 'Pregnancy tracking and reproductive health metrics',
      icon: Icons.timeline,
      headerColor: Colors.purple,
      cardData: pregnancyCards.map((card) => card.toMap()).toList(),
    );
  }

  /// Build pregnancy analytics cards
  List<InfoCardData> _buildPregnancyCards(List<Color> colors) {
    return [
      InfoCardData(
        title: 'Active',
        value: widget.controller.activePregnancies.toString(),
        subtitle: 'ongoing pregnancies',
        icon: Icons.schedule,
        color: colors[0],
        insight: '${(widget.controller.activePregnancies / (widget.controller.totalPregnancyRecords == 0 ? 1 : widget.controller.totalPregnancyRecords) * 100).toStringAsFixed(1)}% active',
      ),
      InfoCardData(
        title: 'Completed',
        value: widget.controller.completedPregnancies.toString(),
        subtitle: 'successful births',
        icon: Icons.check_circle,
        color: colors[1],
        insight: '${(widget.controller.completedPregnancies / (widget.controller.totalPregnancyRecords == 0 ? 1 : widget.controller.totalPregnancyRecords) * 100).toStringAsFixed(1)}% success',
      ),
      InfoCardData(
        title: 'Average Duration',
        value: '${widget.controller.averageGestationPeriod.toStringAsFixed(0)} days',
        subtitle: 'gestation period',
        icon: Icons.timer,
        color: colors[2],
        insight: 'Normal: 280 days',
      ),
      InfoCardData(
        title: 'Due Soon',
        value: widget.controller.upcomingCalvings.toString(),
        subtitle: 'next 30 days',
        icon: Icons.notification_important,
        color: colors[3],
        insight: 'Requires attention',
      ),
    ];
  }

  Widget _buildBreedingOverview(BuildContext context) {
    final overviewColors = _getOverviewColors();
    final overviewCards = _buildOverviewCards(overviewColors);

    return _buildGridSection(
      title: 'Breeding Overview',
      subtitle: 'Overall breeding program performance and insights',
      icon: Icons.assessment,
      headerColor: Colors.green,
      cardData: overviewCards.map((card) => card.toMap()).toList(),
    );
  }

  /// Build overview cards
  List<InfoCardData> _buildOverviewCards(List<Color> overviewColors) {
    return [
      InfoCardData(
        title: 'Success Rate',
        value: '${widget.controller.conceptionRate.toStringAsFixed(1)}%',
        subtitle: 'breeding efficiency',
        icon: Icons.trending_up,
        color: overviewColors[0],
        insight: 'Target: >80%',
      ),
      InfoCardData(
        title: 'Calving Rate',
        value: '${widget.controller.calvingRate.toStringAsFixed(1)}%',
        subtitle: 'successful births',
        icon: Icons.child_care,
        color: overviewColors[1],
        insight: 'Birth success rate',
      ),
      InfoCardData(
        title: 'Calving Interval',
        value: '${widget.controller.averageCalvingInterval.toStringAsFixed(0)} days',
        subtitle: 'between calvings',
        icon: Icons.schedule,
        color: overviewColors[2],
        insight: 'Breeding efficiency',
      ),
      InfoCardData(
        title: 'Breeding Cost',
        value: '£${widget.controller.analytics.averageBreedingCost.toStringAsFixed(0)}',
        subtitle: 'average per attempt',
        icon: Icons.attach_money,
        color: overviewColors[3],
        insight: 'Cost per breeding',
      ),
    ];
  }

  Widget _buildEnhancedLegend(Map<String, int> data, Map<String, Color> colors) {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: data.entries.map((entry) {
        final percentage = widget.controller.totalBreedingRecords > 0
          ? ((entry.value / widget.controller.totalBreedingRecords) * 100).toStringAsFixed(1)
          : '0';
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: colors[entry.key]?.withValues(alpha: 0.1) ?? _fallbackColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: colors[entry.key] ?? _fallbackColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '${entry.key} (${entry.value}) $percentage%',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // Color Management - Different colors for each KPI card (multi-color rule)
  List<Color> _getKPIColors() => AppColors.breedingKpiColors;
  List<Color> _getInsightColors() => AppColors.breedingKpiColors;
  List<Color> _getOverviewColors() => AppColors.breedingKpiColors;

  Map<String, Color> _getBreedingStatusColors() => AppColors.breedingStatusColors;
  Map<String, Color> _getPregnancyStatusColors() => AppColors.breedingPregnancyStatusColors;
}
