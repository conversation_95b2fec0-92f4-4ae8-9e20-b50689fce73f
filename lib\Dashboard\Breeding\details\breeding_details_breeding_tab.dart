import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../controllers/breeding_details_controller.dart';
import '../models/breeding_record_isar.dart';
import '../dialogs/breeding_form_dialog.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../utils/message_utils.dart';
import '../../../Dashboard/widgets/universal_record_card.dart';
import '../../../Dashboard/Cattle/widgets/eligibility_card.dart';
import '../../../Dashboard/Cattle/widgets/stats_card.dart';



class BreedingDetailsBreedingTab extends StatefulWidget {
  const BreedingDetailsBreedingTab({super.key});

  @override
  State<BreedingDetailsBreedingTab> createState() => _BreedingDetailsBreedingTabState();
}

class _BreedingDetailsBreedingTabState extends State<BreedingDetailsBreedingTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Consumer<BreedingDetailsController>(
      builder: (context, controller, child) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        final breedingRecords = controller.breedingRecords;

        if (breedingRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Breeding Records',
            message: 'No breeding attempts recorded for ${controller.cattle?.name ?? 'this cattle'}.',
            tabColor: AppColors.breedingKpiColors[0],
            tabIndex: 0,
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () => _addBreedingRecord(context, controller),
              tabColor: AppColors.breedingKpiColors[0],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80), // Bottom padding for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Stats card at top
              if (breedingRecords.isNotEmpty) ...[
                _buildStatsCard(controller),
                const SizedBox(height: 16),
              ],
              // Status/eligibility card second
              if (controller.cattle != null) ...[
                _buildEligibilityCard(controller),
                const SizedBox(height: 16),
              ],
              // Records list
              _buildRecordsList(breedingRecords),
            ],
          ),
        );
      },
    );
  }



  Widget _buildRecordsList(List<BreedingRecordIsar> records) {
    // Sort records by date (most recent first)
    final sortedRecords = List<BreedingRecordIsar>.from(records)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));

    return Column(
      children: sortedRecords.map((record) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: _buildRecordCard(record),
      )).toList(),
    );
  }

  Widget _buildRecordCard(BreedingRecordIsar record) {
    // Format dates
    String dateText = record.date != null
        ? DateFormat('MMM dd, yyyy').format(record.date!)
        : 'Unknown date';

    // Format method and status
    String methodText = record.method ?? 'Unknown method';
    String statusText = record.status ?? 'Unknown status';

    // Format bull and expected date for row 3
    String bullText = record.bullIdOrType?.isNotEmpty == true
        ? 'Bull: ${record.bullIdOrType}'
        : 'Bull: Unknown';
    String expectedText = record.expectedDate != null
        ? 'Expected: ${DateFormat('MMM dd, yyyy').format(record.expectedDate!)}'
        : 'Expected: Unknown';

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: methodText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.science,
      row2Left: statusText,
      row2Right: record.cost != null ? '\$${record.cost!.toStringAsFixed(2)}' : 'No cost',
      row2LeftIcon: _getStatusIcon(record.status),
      row2RightIcon: Icons.attach_money,
      row3Left: bullText,
      row3Right: expectedText,
      row3LeftIcon: Icons.pets,
      row3RightIcon: Icons.event_available,
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.breedingHeader,
      onTap: () => _viewRecordDetails(record),
      onEdit: () => _editRecord(record),
      onDelete: () => _deleteRecord(record),
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'completed':
        return Icons.check_circle;
      case 'failed':
        return Icons.cancel;
      case 'pending':
        return Icons.schedule;
      case 'confirmed':
        return Icons.verified;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildEligibilityCard(BreedingDetailsController controller) {
    final cattle = controller.cattle!;

    // Convert breeding records to the format expected by EligibilityCard
    final breedingRecordsData = controller.breedingRecords.map((record) => {
      'date': record.date,
      'status': record.status,
    }).toList();

    // Get the most recent breeding date (sorted by date, most recent first)
    final sortedBreedingRecords = List<BreedingRecordIsar>.from(controller.breedingRecords)
      ..sort((a, b) {
        if (a.date == null && b.date == null) return 0;
        if (a.date == null) return 1;
        if (b.date == null) return -1;
        return b.date!.compareTo(a.date!);
      });

    return EligibilityCard.breeding(
      gender: cattle.gender.name,
      cattleId: cattle.businessId ?? '',
      animalTypeId: cattle.animalTypeId ?? '',
      isPregnant: controller.isCurrentlyPregnant,
      dateOfBirth: cattle.dateOfBirth,
      purchaseDate: cattle.purchaseDate,
      lastBreedingDate: sortedBreedingRecords.isNotEmpty
          ? sortedBreedingRecords.first.date
          : null,
      lastCalvingDate: cattle.breedingStatus?.lastCalvingDate,
      breedingRecords: breedingRecordsData,
      onAddPressed: () => _addBreedingRecord(context, controller),
    );
  }

  Widget _buildStatsCard(BreedingDetailsController controller) {
    // Use controller's enhanced statistics

    return StatsCard.breedingStats(
      totalBreedings: controller.totalBreedingAttempts,
      pendingBreedings: controller.pendingBreedings,
      confirmedBreedings: controller.confirmedBreedings,
      completedBreedings: controller.completedBreedings,
      failedBreedings: controller.failedBreedings,
      onViewAllTap: () {
        // Optional: Navigate to detailed breeding records view
      },
      onInfoTap: () {
        // Show breeding stats information
        _showBreedingStatsInfo();
      },
    );
  }

  void _showBreedingStatsInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Breeding Statistics Info'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('These statistics show the breeding history for this cattle:'),
            SizedBox(height: 8),
            Text('• Total: All breeding attempts for this cattle'),
            Text('• Pending: Breeding attempts awaiting confirmation'),
            Text('• Confirmed: Confirmed pregnancies awaiting delivery'),
            Text('• Completed: Successfully completed breedings with calving'),
            Text('• Failed: Unsuccessful breeding attempts'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _addBreedingRecord(BuildContext context, BreedingDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => BreedingFormDialog(
        initialCattleId: controller.cattle!.tagId, // Use tagId for breeding records
        onSave: (record) async {
          final success = await controller.addBreedingRecord(record);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Breeding record added successfully');
          }
        },
      ),
    );
  }

  void _viewRecordDetails(BreedingRecordIsar record) {
    // TODO: Implement record details view
    MessageUtils.showInfo(context, 'Record details view not yet implemented');
  }

  // Removed unused _handleMenuAction method

  void _editRecord(BreedingRecordIsar record) {
    final controller = Provider.of<BreedingDetailsController>(context, listen: false);
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => BreedingFormDialog(
        record: record,
        initialCattleId: controller.cattle!.businessId,
        onSave: (updatedRecord) async {
          final success = await controller.updateBreedingRecord(updatedRecord);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Breeding record updated successfully');
          }
        },
      ),
    );
  }

  void _deleteRecord(BreedingRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Breeding Record'),
        content: const Text('Are you sure you want to delete this breeding record? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final controller = Provider.of<BreedingDetailsController>(context, listen: false);
              final success = await controller.deleteBreedingRecord(record.businessId!);
              if (!mounted) return;
              if (success) {
                MessageUtils.showSuccess(context, 'Breeding record deleted successfully');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
