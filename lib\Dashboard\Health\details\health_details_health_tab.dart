import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../models/health_record_isar.dart';
import '../controllers/health_details_controller.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/message_utils.dart';


class HealthDetailsHealthTab extends StatefulWidget {
  const HealthDetailsHealthTab({super.key});

  @override
  State<HealthDetailsHealthTab> createState() => _HealthDetailsHealthTabState();
}

class _HealthDetailsHealthTabState extends State<HealthDetailsHealthTab> {

  @override
  Widget build(BuildContext context) {
    return Consumer<HealthDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final healthRecords = controller.healthRecords;

        if (cattle == null) {
          return const Center(child: Text('No cattle data available'));
        }

        if (healthRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Health Records',
            message: 'No health records found for ${cattle.name ?? cattle.tagId}',
            tabColor: AppColors.healthHeader,
            tabIndex: 1, // Health tab
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80), // Bottom padding for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Records list
              _buildRecordsList(healthRecords),
            ],
          ),
        );
      },
    );
  }

  /// Build records list
  Widget _buildRecordsList(List<HealthRecordIsar> healthRecords) {
    return Column(
      children: healthRecords.map((record) => Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: _buildHealthRecordCard(record),
      )).toList(),
    );
  }

  Widget _buildHealthRecordCard(HealthRecordIsar record) {
    // Format row 1: Date (since cattle context is already known in detail view)
    String dateText = record.date != null
        ? DateFormat('MMM dd, yyyy').format(record.date!)
        : 'No date';

    // Format row 2: Issue + Treatment (matching main health tab logic)
    String healthIssue = record.details ?? 'No issue specified';
    String treatment = record.treatment ?? 'No treatment';

    // Format row 3: Veterinarian + Cost (conditional display)
    String? veterinarian = record.veterinarian?.isNotEmpty == true
        ? record.veterinarian
        : null;
    String? cost = record.cost != null && record.cost! > 0
        ? '\$${record.cost!.toStringAsFixed(2)}'
        : null;

    return UniversalRecordCard(
      // Row 1: Date + Status/Type (since cattle context is known)
      row1Left: dateText,
      row1Right: record.status ?? 'Active',
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.info_outline,

      // Row 2: Issue + Treatment (matching main health tab pattern)
      row2Left: healthIssue,
      row2Right: treatment,
      row2LeftIcon: Icons.medical_information,
      row2RightIcon: Icons.healing,

      // Row 3: Veterinarian + Cost (conditional display only when data exists)
      row3Left: veterinarian,
      row3Right: cost,
      row3LeftIcon: veterinarian != null ? Icons.person : null,
      row3RightIcon: cost != null ? Icons.receipt_long : null,

      // Notes
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.healthHeader,

      onTap: () => _showRecordDetails(record),
      onEdit: () => _editRecord(record),
      onDelete: () => _deleteRecord(record),
    );
  }



  void _showRecordDetails(HealthRecordIsar record) {
    // Show detailed view in a dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Health Record Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Date', record.date != null
                  ? DateFormat('MMMM dd, yyyy').format(record.date!)
                  : 'Not specified'),
              _buildDetailRow('Issue', record.details ?? 'No issue specified'),
              _buildDetailRow('Treatment', record.treatment ?? 'No treatment'),
              if (record.veterinarian?.isNotEmpty == true)
                _buildDetailRow('Veterinarian', record.veterinarian!),
              if (record.cost != null && record.cost! > 0)
                _buildDetailRow('Cost', '\$${record.cost!.toStringAsFixed(2)}'),
              if (record.notes?.isNotEmpty == true)
                _buildDetailRow('Notes', record.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editRecord(HealthRecordIsar record) {
    final controller = Provider.of<HealthDetailsController>(context, listen: false);

    print('🔧 HEALTH DETAILS TAB: Opening edit dialog for record ${record.id}');
    showDialog(
      context: context,
      builder: (context) => HealthRecordFormDialog(
        healthRecord: record,
        cattle: controller.cattle != null ? [controller.cattle!] : [],
        onSave: (updatedRecord) async {
          try {
            print('💾 HEALTH DETAILS TAB: Starting update for record ${updatedRecord.id}');

            await controller.updateHealthRecord(updatedRecord);

            print('✅ HEALTH DETAILS TAB: Update completed for record ${updatedRecord.id}');

            if (mounted) {
              MessageUtils.showSuccess(context, 'Health record updated successfully');
            }
          } catch (e) {
            print('❌ HEALTH DETAILS TAB: Update failed for record ${updatedRecord.id}: $e');
            if (mounted) {
              MessageUtils.showError(context, 'Failed to update health record: $e');
            }
          }
        },
      ),
    );
  }

  void _deleteRecord(HealthRecordIsar record) {
    final controller = Provider.of<HealthDetailsController>(context, listen: false);
    final cattle = controller.cattle;
    final recordDescription = 'Health record for ${cattle?.name ?? 'this cattle'}';

    print('🗑️ HEALTH DETAILS TAB: Opening delete confirmation for record ${record.id}');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Health Record'),
        content: Text('Are you sure you want to delete this $recordDescription?'),
        actions: [
          TextButton(
            onPressed: () {
              print('❌ HEALTH DETAILS TAB: Delete cancelled for record ${record.id}');
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                print('🗑️ HEALTH DETAILS TAB: Starting delete for record ${record.id}');

                await controller.deleteHealthRecord(record.id);

                print('✅ HEALTH DETAILS TAB: Delete completed for record ${record.id}');

                if (mounted) {
                  MessageUtils.showSuccess(context, 'Health record deleted successfully');
                }
              } catch (e) {
                print('❌ HEALTH DETAILS TAB: Delete failed for record ${record.id}: $e');
                if (mounted) {
                  MessageUtils.showError(context, 'Failed to delete health record: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
