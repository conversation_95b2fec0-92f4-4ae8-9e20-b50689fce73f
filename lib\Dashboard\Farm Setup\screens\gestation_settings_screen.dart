import 'package:flutter/material.dart';
import '../services/farm_setup_repository.dart';
import 'package:get_it/get_it.dart';
import '../models/animal_type_isar.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/message_utils.dart';

// Define spacing constants for consistency
const double kSpacing = 8.0;
const double kSpacingMedium = 16.0;
const double kSpacingLarge = 24.0;

class GestationSettingsScreen extends StatefulWidget {
  const GestationSettingsScreen({super.key});

  @override
  State<GestationSettingsScreen> createState() =>
      _GestationSettingsScreenState();
}

class _GestationSettingsScreenState extends State<GestationSettingsScreen> {
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  List<AnimalTypeIsar> _types = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnimalTypes();
  }

  Future<void> _loadAnimalTypes() async {
    try {
      final typesData = await _farmSetupRepository.getAllAnimalTypes();

      if (mounted) {
        setState(() {
          _types = typesData;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Error loading animal types: $e');
        setState(() {
          _types = [];
          _isLoading = false;
        });
      }
    }
  }

  // Optimized to update local state directly
  Future<void> _saveSettings(AnimalTypeIsar type) async {
    try {
      await _farmSetupRepository.addOrUpdateAnimalType(type);

      if (mounted) {
        // Update the local state
        setState(() {
          final index = _types.indexWhere((t) => t.id == type.id);
          if (index != -1) {
            _types[index] = type;
          } else {
            debugPrint("WARN: Updated animal type not found in local list.");
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showEditDialog(AnimalTypeIsar type) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: _EditGestationForm(
            initialType: type,
            onSave: (updatedType) {
              _saveSettings(updatedType);
              Navigator.of(context).pop();
            },
            onCancel: () => Navigator.of(context).pop(),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pets_outlined,
            size: 64,
            color: theme.colorScheme.primary.withAlpha(128),
          ),
          const SizedBox(height: kSpacingMedium),
          Text(
            'No Animal Types Found',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: kSpacing),
          Text(
            'Please add animal types in Farm Setup first',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Gestation Settings',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppColors.primary))
          : _types.isEmpty
              ? _buildEmptyState()
              : AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: ListView.builder(
                    key: const ValueKey('animal_types_list'),
                    padding: const EdgeInsets.all(kSpacingMedium),
                    itemCount: _types.length,
                    itemBuilder: (context, index) {
                      final type = _types[index];
                      return Card(
                        elevation: 2,
                        margin: const EdgeInsets.only(bottom: kSpacingMedium),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(kSpacingMedium),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Header row with icon and edit button
                              Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor: type.color,
                                    radius: 24,
                                    child: Icon(
                                      type.icon,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: kSpacingMedium),
                                  Expanded(
                                    child: Text(
                                      type.name ?? 'Unknown Type',
                                      style:
                                          theme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.edit,
                                      color: AppColors.primary,
                                    ),
                                    onPressed: () => _showEditDialog(type),
                                  ),
                                ],
                              ),
                              const SizedBox(height: kSpacingMedium),
                              // Settings detail rows
                              _buildSettingRow(
                                Icons.pregnant_woman,
                                'Gestation Period',
                                '${type.defaultGestationDays ?? 0} days',
                                theme,
                              ),
                              _buildSettingRow(
                                Icons.sync,
                                'Heat Cycle',
                                '${type.defaultHeatCycleDays ?? 0} days',
                                theme,
                              ),
                              _buildSettingRow(
                                Icons.hourglass_empty,
                                'Empty Period',
                                '${type.defaultEmptyPeriodDays ?? 0} days',
                                theme,
                              ),
                              _buildSettingRow(
                                Icons.cake,
                                'Breeding Age',
                                '${_daysToMonths(type.defaultBreedingAge ?? 0)} months',
                                theme,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
    );
  }

  Widget _buildSettingRow(
      IconData icon, String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: kSpacing),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primary.withAlpha(179),
            size: 20,
          ),
          const SizedBox(width: kSpacing),
          Expanded(
            child: Text(
              label,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to convert days to months for display
  int _daysToMonths(int days) {
    return (days / 30).round();
  }

  // Helper method to convert months to days for storage
}

// Dedicated StatefulWidget for the edit form to properly manage controllers
class _EditGestationForm extends StatefulWidget {
  final AnimalTypeIsar initialType;
  final Function(AnimalTypeIsar) onSave;
  final VoidCallback onCancel;

  const _EditGestationForm({
    required this.initialType,
    required this.onSave,
    required this.onCancel,
  });

  @override
  _EditGestationFormState createState() => _EditGestationFormState();
}

class _EditGestationFormState extends State<_EditGestationForm> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _gestationController;
  late TextEditingController _heatCycleController;
  late TextEditingController _emptyPeriodController;
  late TextEditingController _breedingAgeController;

  @override
  void initState() {
    super.initState();
    // Initialize controllers with values from the AnimalTypeIsar
    _gestationController = TextEditingController(
      text: widget.initialType.defaultGestationDays?.toString() ?? "0",
    );
    _heatCycleController = TextEditingController(
      text: widget.initialType.defaultHeatCycleDays?.toString() ?? "0",
    );
    _emptyPeriodController = TextEditingController(
      text: widget.initialType.defaultEmptyPeriodDays?.toString() ?? "0",
    );
    // Convert breeding age from days to months for display
    final breedingAgeMonths = widget.initialType.defaultBreedingAge != null
        ? (widget.initialType.defaultBreedingAge! / 30).round().toString()
        : "0";
    _breedingAgeController = TextEditingController(
      text: breedingAgeMonths,
    );
  }

  @override
  void dispose() {
    // Properly dispose controllers
    _gestationController.dispose();
    _heatCycleController.dispose();
    _emptyPeriodController.dispose();
    _breedingAgeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(kSpacingMedium),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: AppColors.primary),
                const SizedBox(width: kSpacing),
                Text(
                  'Edit ${widget.initialType.name} Settings',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingLarge),

            // Gestation Period Field
            _buildTextField(
              controller: _gestationController,
              labelText: 'Gestation Period (days)',
              hintText: 'Enter gestation period in days',
              icon: Icons.pregnant_woman,
            ),
            const SizedBox(height: kSpacingMedium),

            // Heat Cycle Field
            _buildTextField(
              controller: _heatCycleController,
              labelText: 'Heat Cycle (days)',
              hintText: 'Enter heat cycle in days',
              icon: Icons.sync,
            ),
            const SizedBox(height: kSpacingMedium),

            // Empty Period Field
            _buildTextField(
              controller: _emptyPeriodController,
              labelText: 'Empty Period (days)',
              hintText: 'Enter empty period in days',
              icon: Icons.hourglass_empty,
            ),
            const SizedBox(height: kSpacingMedium),

            // Breeding Age Field
            _buildTextField(
              controller: _breedingAgeController,
              labelText: 'Breeding Age (months)',
              hintText: 'Enter breeding age in months',
              icon: Icons.cake,
            ),
            const SizedBox(height: kSpacingLarge),

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: widget.onCancel,
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: theme.colorScheme.primary.withAlpha(204),
                    ),
                  ),
                ),
                const SizedBox(width: kSpacingMedium),
                ElevatedButton(
                  onPressed: _saveForm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: kSpacingMedium,
                      vertical: kSpacing,
                    ),
                  ),
                  child: const Text('Save Settings'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData icon,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        prefixIcon: Icon(icon, color: AppColors.primary.withAlpha(179)),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a value';
        }
        final days = int.tryParse(value);
        if (days == null || days < 0) {
          return 'Please enter a valid number (≥ 0)';
        }
        return null;
      },
    );
  }

  void _saveForm() {
    if (_formKey.currentState!.validate()) {
      final updatedType = widget.initialType.copyWith(
        defaultGestationDays: int.parse(_gestationController.text),
        defaultHeatCycleDays: int.parse(_heatCycleController.text),
        defaultEmptyPeriodDays: int.parse(_emptyPeriodController.text),
        // Convert breeding age from months to days for storage
        defaultBreedingAge: int.parse(_breedingAgeController.text) * 30,
      );
      widget.onSave(updatedType);
    }
  }
}
