# Real-Time Streams Implementation Guide

## Overview
This guide explains how to implement reliable real-time streams in modules based on the successful breeding module fix. The key is using **individual streams** instead of **StreamZip** for better Isar database change detection.

## Problem: StreamZip Issues
**❌ Don't use StreamZip** - It doesn't reliably detect Isar database changes:
```dart
// DON'T DO THIS - StreamZip doesn't work reliably
final combinedStream = StreamZip([
  _repository.watchAllRecords(),
  _cattleRepository.watchAllCattle(),
]);
```

## Solution: Individual Streams Pattern
**✅ Use individual streams** - Following the cattle module pattern:

### 1. Stream Subscriptions Declaration
```dart
class YourController extends ChangeNotifier {
  // Individual stream subscriptions - NOT StreamZip
  StreamSubscription<List<YourRecordIsar>>? _recordsStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;
  
  // Data storage
  List<YourRecordIsar> _unfilteredRecords = [];
  List<CattleIsar> _unfilteredCattle = [];
  List<YourRecordIsar> _filteredRecords = [];
  bool _hasActiveFilters = false;
}
```

### 2. Stream Initialization
```dart
void _initializeStreamListeners() {
  debugPrint('🔧 YOUR_MODULE: Initializing stream listeners...');
  
  // Primary records stream
  _recordsStreamSubscription = _isar.yourRecordIsars.where()
      .watch(fireImmediately: true)
      .listen((records) {
    debugPrint('🔄 RECORDS STREAM: Received ${records.length} records');
    _unfilteredRecords = records;
    _updateFilteredDataAndNotify();
  }, onError: (error) {
    debugPrint('❌ Records stream error: $error');
  });

  // Cattle stream (for relationships)
  _cattleStreamSubscription = _isar.cattleIsars.where()
      .watch(fireImmediately: true)
      .listen((cattle) {
    debugPrint('🔄 CATTLE STREAM: Received ${cattle.length} cattle');
    _unfilteredCattle = cattle;
    _updateAnalytics();
  }, onError: (error) {
    debugPrint('❌ Cattle stream error: $error');
  });

  _hasActiveFilters = false;
  debugPrint('✅ YOUR_MODULE: Stream listeners initialized');
}
```

### 3. Helper Methods
```dart
/// Update filtered data and notify listeners
void _updateFilteredDataAndNotify() {
  try {
    debugPrint('📊 YOUR_MODULE: Updating filtered data and UI');
    
    // Update filtered data if no filters are active
    if (!_hasActiveFilters) {
      _filteredRecords = List.from(_unfilteredRecords);
      _setState(ControllerState.loaded);
    }

    debugPrint('🔔 YOUR_MODULE: Notifying UI listeners');
    notifyListeners();
  } catch (e) {
    debugPrint('Error updating filtered data: $e');
    _setState(ControllerState.error);
    _errorMessage = 'Failed to update data: $e';
    notifyListeners();
  }
}

/// Update analytics calculations
void _updateAnalytics() async {
  try {
    // Calculate analytics using unfiltered data
    _calculateAnalytics();
  } catch (e) {
    debugPrint('Error updating analytics: $e');
  }
}
```

### 4. CRUD Operations (No Manual Refresh Needed)
```dart
/// Add new record - stream handles UI update automatically
Future<void> addRecord(YourRecordIsar record) async {
  debugPrint('🎯 YOUR_MODULE: Adding record');
  debugPrint('   Record ID: ${record.businessId}');

  await _repository.saveRecord(record);
  debugPrint('✅ YOUR_MODULE: Record saved, stream will handle UI update...');
  // NO manual refresh needed - stream will fire automatically
}

/// Delete record - stream handles UI update automatically  
Future<void> deleteRecord(String businessId) async {
  debugPrint('🎯 YOUR_MODULE: Deleting record with business ID: $businessId');
  
  // Find record by businessId, delete by Isar ID
  final record = _unfilteredRecords.firstWhere(
    (r) => r.businessId == businessId,
    orElse: () => throw Exception('Record not found'),
  );
  
  await _repository.deleteRecord(record.id);
  debugPrint('✅ YOUR_MODULE: Delete completed, stream will handle UI update...');
  // NO manual refresh needed - stream will fire automatically
}
```

### 5. Cleanup
```dart
@override
void dispose() {
  // Cancel all stream subscriptions
  _recordsStreamSubscription?.cancel();
  _cattleStreamSubscription?.cancel();
  _filteredStreamSubscription?.cancel();
  super.dispose();
}
```

## Key Success Patterns

### ✅ DO:
- Use individual streams with direct Isar access
- Use `fireImmediately: true` for instant data loading
- Handle each stream separately in its own listener
- Use business IDs in UI, convert to Isar IDs in controller
- Let streams handle UI updates automatically
- Follow the cattle module pattern exactly

### ❌ DON'T:
- Use StreamZip for combining streams
- Call manual refresh after CRUD operations
- Use Isar IDs directly in UI components
- Mix repository pattern with direct Isar in streams
- Forget to cancel stream subscriptions in dispose

## Expected Logs for Working Streams
```
🔧 YOUR_MODULE: Initializing stream listeners...
✅ YOUR_MODULE: Stream listeners initialized
🔄 RECORDS STREAM: Received 1 records
📊 YOUR_MODULE: Updating filtered data and UI
🔔 YOUR_MODULE: Notifying UI listeners
```

## Troubleshooting

### Stream Not Firing After Database Changes
- **Check**: Are you using StreamZip? Replace with individual streams
- **Check**: Are you using repository pattern in streams? Use direct Isar access
- **Check**: Is `fireImmediately: true` set?

### UI Not Updating
- **Check**: Are you calling `notifyListeners()` in stream handlers?
- **Check**: Are filtered records being updated when unfiltered data changes?
- **Check**: Is the controller properly connected to UI with Provider?

### Records Not Deleting
- **Check**: Are you using business IDs in UI and converting to Isar IDs in controller?
- **Check**: Is the business ID lookup finding the correct record?

## Migration Checklist
- [ ] Replace StreamZip with individual streams
- [ ] Use direct Isar access in streams (`_isar.yourRecords.where().watch()`)
- [ ] Add separate stream handlers for each data type
- [ ] Remove manual refresh calls from CRUD operations
- [ ] Update dispose method to cancel all streams
- [ ] Test create/update/delete operations for real-time updates
- [ ] Verify logs show individual stream updates

This pattern ensures reliable real-time synchronization across all modules!
