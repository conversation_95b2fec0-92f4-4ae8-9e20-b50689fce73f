import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:logging/logging.dart';

// Core services
import '../services/database/isar_service.dart';
import 'default_data_seeder.dart';

// Repositories (consistent naming convention)
import '../Dashboard/Farm Setup/services/farm_setup_repository.dart';
import '../Dashboard/Cattle/services/cattle_repository.dart';
import '../Dashboard/Cattle/services/cattle_insights_service.dart';
import '../Dashboard/Health/services/health_repository.dart';
import '../Dashboard/Health/services/health_insights_service.dart';
import '../Dashboard/Breeding/services/breeding_repository.dart';
import '../Dashboard/Breeding/services/breeding_insights_service.dart';
import '../Dashboard/Events/services/events_repository.dart';
import '../Dashboard/Events/services/events_insights_service.dart';
import '../Dashboard/Milk Records/services/milk_repository.dart';
import '../Dashboard/Milk Records/services/milk_insights_service.dart';
import '../Dashboard/Weight/services/weight_repository.dart';
import '../Dashboard/Weight/services/weight_insights_service.dart';
import '../Dashboard/Transactions/services/transactions_repository.dart';
// import '../Dashboard/Reports/services/reports_repository.dart';
// import '../Dashboard/Notifications/services/notifications_repository.dart';

// Controllers are now managed by Provider, not GetIt
// import '../Dashboard/Settings/services/settings_repository.dart';
import '../services/validation/validation_service.dart';

/// Centralized dependency injection setup
/// Replaces the God Object pattern with clean, testable DI
class DependencyInjection {
  static final Logger _logger = Logger('DependencyInjection');
  static bool _isInitialized = false;

  /// Initialize all dependencies
  /// This replaces DatabaseHelper and IsarInitializer
  static Future<void> initDependencies() async {
    if (_isInitialized) {
      _logger.info('Dependencies already initialized');
      return;
    }

    try {
      _logger.info('Initializing dependencies...');
      final getIt = GetIt.instance;

      // 1. Register core Isar service first
      await _registerCoreServices(getIt);

      // 2. Register all repositories/handlers
      await _registerRepositories(getIt);

      // 3. Register utility services (like DefaultDataSeeder)
      // Note: Controllers are now managed by Provider, not GetIt
      registerUtilityServices(getIt);

      _isInitialized = true;
      _logger.info('Dependencies initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing dependencies: $e');
      rethrow;
    }
  }

  /// Register core services (IsarService, Isar instance)
  static Future<void> _registerCoreServices(GetIt getIt) async {
    _logger.info('Registering core services...');

    // Register IsarService as singleton with proper DI pattern
    if (!getIt.isRegistered<IsarService>()) {
      // Create the instance, initialize it, then register it
      final isarService = IsarService();
      await isarService.initialize(); // Initialize before registering
      getIt.registerSingleton<IsarService>(isarService);
      _logger.info('IsarService registered');
    }

    // Register Isar instance for direct access
    if (!getIt.isRegistered<Isar>()) {
      final isarService = getIt<IsarService>();
      getIt.registerSingleton<Isar>(isarService.isar);
      _logger.info('Isar instance registered');
    }
  }

  /// Register all repositories as singletons
  static Future<void> _registerRepositories(GetIt getIt) async {
    _logger.info('Registering repositories...');

    // Farm Setup - with explicit dependency
    if (!getIt.isRegistered<FarmSetupRepository>()) {
      getIt.registerSingleton<FarmSetupRepository>(
        FarmSetupRepository(getIt<IsarService>()),
      );
    }

    // Cattle - register CattleRepository with explicit dependency
    if (!getIt.isRegistered<CattleRepository>()) {
      getIt.registerSingleton<CattleRepository>(
        CattleRepository(getIt<IsarService>()),
      );
    }

    // Cattle Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<CattleInsightsService>()) {
      getIt.registerSingleton<CattleInsightsService>(
        CattleInsightsService(),
      );
    }

    // Health - with explicit dependency
    if (!getIt.isRegistered<HealthRepository>()) {
      getIt.registerSingleton<HealthRepository>(
        HealthRepository(getIt<IsarService>()),
      );
    }

    // Health Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<HealthInsightsService>()) {
      getIt.registerSingleton<HealthInsightsService>(
        HealthInsightsService(),
      );
    }

    // Breeding - with explicit dependency
    if (!getIt.isRegistered<BreedingRepository>()) {
      getIt.registerSingleton<BreedingRepository>(
        BreedingRepository(getIt<IsarService>()),
      );
    }

    // Breeding Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<BreedingInsightsService>()) {
      getIt.registerSingleton<BreedingInsightsService>(
        BreedingInsightsService(),
      );
    }

    // Events - with explicit dependency
    if (!getIt.isRegistered<EventsRepository>()) {
      getIt.registerSingleton<EventsRepository>(
        EventsRepository(getIt<IsarService>()),
      );
    }

    // Events Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<EventsInsightsService>()) {
      getIt.registerSingleton<EventsInsightsService>(
        EventsInsightsService(),
      );
    }

    // Milk - with explicit dependency
    if (!getIt.isRegistered<MilkRepository>()) {
      getIt.registerSingleton<MilkRepository>(
        MilkRepository(getIt<IsarService>()),
      );
    }

    // Milk Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<MilkInsightsService>()) {
      getIt.registerSingleton<MilkInsightsService>(
        MilkInsightsService(),
      );
    }

    // Weight - with explicit dependency
    if (!getIt.isRegistered<WeightRepository>()) {
      getIt.registerSingleton<WeightRepository>(
        WeightRepository(getIt<IsarService>()),
      );
    }

    // Weight Insights Service - stateless service for insights generation
    if (!getIt.isRegistered<WeightInsightsService>()) {
      getIt.registerSingleton<WeightInsightsService>(
        WeightInsightsService(),
      );
    }

    // Transactions - with explicit dependency
    if (!getIt.isRegistered<TransactionsRepository>()) {
      getIt.registerSingleton<TransactionsRepository>(
        TransactionsRepository(getIt<IsarService>()),
      );
    }

    // Reports - with explicit dependency
    // if (!getIt.isRegistered<ReportsRepository>()) {
    //   getIt.registerSingleton<ReportsRepository>(
    //     ReportsRepository(),
    //   );
    // }

    // Notifications - with explicit dependency
    // if (!getIt.isRegistered<NotificationsRepository>()) {
    //   getIt.registerSingleton<NotificationsRepository>(
    //     NotificationsRepository(getIt<IsarService>()),
    //   );
    // }

    // Settings - with explicit dependency
    // if (!getIt.isRegistered<SettingsRepository>()) {
    //   getIt.registerSingleton<SettingsRepository>(
    //     SettingsRepository(getIt<IsarService>()),
    //   );
    // }

    // ValidationService - with explicit dependencies
    if (!getIt.isRegistered<ValidationService>()) {
      getIt.registerSingleton<ValidationService>(
        ValidationService(
          getIt<CattleRepository>(),
          getIt<FarmSetupRepository>(),
          // getIt<HealthRepository>(),
          // getIt<BreedingRepository>(),
          // getIt<TransactionsRepository>(),
        ),
      );
    }

    _logger.info('All repositories registered successfully');
  }

  // Controllers are now managed by Provider, not GetIt
  // This method is no longer needed

  /// Register utility services like DefaultDataSeeder
  static void registerUtilityServices(GetIt getIt) {
    _logger.info('Registering utility services...');

    // DefaultDataSeeder - with explicit dependencies (Pure DI pattern)
    if (!getIt.isRegistered<DefaultDataSeeder>()) {
      getIt.registerSingleton<DefaultDataSeeder>(
        DefaultDataSeeder(
          getIt<IsarService>(),
          getIt<FarmSetupRepository>(),
          // getIt<EventsRepository>(),
          getIt<TransactionsRepository>(),
          // getIt<SettingsRepository>(),
        ),
      );
    }

    _logger.info('All utility services registered successfully');
  }

  /// Reset all dependencies (useful for testing)
  static Future<void> reset() async {
    _logger.info('Resetting dependencies...');
    await GetIt.instance.reset();
    _isInitialized = false;
    _logger.info('Dependencies reset');
  }

  /// Check if dependencies are initialized
  static bool get isInitialized => _isInitialized;
}
