import '../models/weight_record_isar.dart';
import '../models/weight_insights_models.dart';
// Unused import removed

/// Pure analytics service for weight calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class WeightAnalyticsService {
  
  /// Main calculation method for individual cattle analytics
  /// Single entry point with O(n) efficiency
  static AnalyticsSummary calculateSummary(List<WeightRecordIsar> records) {
    if (records.isEmpty) {
      return AnalyticsSummary.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _WeightAnalyticsAccumulator();
    
    for (final record in records) {
      accumulator.process(record);
    }
    
    return accumulator.toSummary();
  }

  /// Prepare a new record by calculating growth metrics based on the previous one
  /// This replaces the old repository._calculateGrowthData method
  static WeightRecordIsar populateGrowthData(
    WeightRecordIsar newRecord, 
    WeightRecordIsar? previousRecord
  ) {
    if (previousRecord == null) {
      // First record for this cattle - no growth data to calculate
      return newRecord;
    }

    // Calculate growth metrics
    newRecord.previousWeight = previousRecord.weight;
    newRecord.weightGain = newRecord.weight - previousRecord.weight;

    // Calculate days since last measurement
    if (previousRecord.measurementDate != null && newRecord.measurementDate != null) {
      final daysDiff = newRecord.measurementDate!
          .difference(previousRecord.measurementDate!)
          .inDays;
      newRecord.daysSinceLastMeasurement = daysDiff;

      // Calculate daily gain
      if (daysDiff > 0) {
        newRecord.dailyGain = newRecord.weightGain! / daysDiff;
      }
    }

    return newRecord;
  }

  /// Calculate average daily gain for a cattle over a period
  static double calculateAverageDailyGain(List<WeightRecordIsar> records) {
    if (records.length < 2) return 0.0;

    // Sort by date to ensure proper calculation
    final sortedRecords = List<WeightRecordIsar>.from(records)
      ..sort((a, b) => (a.measurementDate ?? DateTime.now())
          .compareTo(b.measurementDate ?? DateTime.now()));

    double totalGain = 0.0;
    int totalDays = 0;

    for (int i = 1; i < sortedRecords.length; i++) {
      final current = sortedRecords[i];
      final previous = sortedRecords[i - 1];

      if (current.measurementDate != null && previous.measurementDate != null) {
        final days = current.measurementDate!
            .difference(previous.measurementDate!)
            .inDays;
        
        if (days > 0) {
          totalGain += current.weight - previous.weight;
          totalDays += days;
        }
      }
    }

    return totalDays > 0 ? totalGain / totalDays : 0.0;
  }

  /// Calculate weight trend for a cattle
  static String calculateWeightTrend(List<WeightRecordIsar> records) {
    if (records.length < 3) return 'Insufficient data';

    final sortedRecords = List<WeightRecordIsar>.from(records)
      ..sort((a, b) => (a.measurementDate ?? DateTime.now())
          .compareTo(b.measurementDate ?? DateTime.now()));

    // Calculate trend over last 3 measurements
    final recentRecords = sortedRecords.take(3).toList();
    double totalChange = 0.0;

    for (int i = 1; i < recentRecords.length; i++) {
      totalChange += recentRecords[i].weight - recentRecords[i - 1].weight;
    }

    if (totalChange > 1.0) return 'Gaining';
    if (totalChange < -1.0) return 'Losing';
    return 'Stable';
  }

  /// Calculate individual cattle analytics for detail screens
  /// Reuses existing helper methods to eliminate duplication
  static IndividualCattleAnalytics calculateIndividualAnalytics(
    List<WeightRecordIsar> records,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    if (records.isEmpty) {
      return IndividualCattleAnalytics.empty;
    }

    // Filter by date range if provided
    final filteredRecords = _filterRecordsByDateRange(records, startDate, endDate);

    if (filteredRecords.isEmpty) {
      return IndividualCattleAnalytics.empty;
    }

    // Sort by date using helper method
    final sortedRecords = _sortRecordsByDate(filteredRecords);

    final currentWeight = sortedRecords.last.weight;
    final totalWeight = filteredRecords.map((r) => r.weight).reduce((a, b) => a + b);
    final averageWeight = totalWeight / filteredRecords.length;

    // Reuse existing helper methods instead of duplicating logic
    final weightGain = _calculateTotalWeightGain(sortedRecords);
    final averageDailyGain = calculateAverageDailyGain(filteredRecords);
    final weightTrend = calculateWeightTrend(filteredRecords);

    final dateRange = _formatDateRange(startDate, endDate);

    return IndividualCattleAnalytics(
      currentWeight: currentWeight,
      averageWeight: averageWeight,
      weightGain: weightGain,
      averageDailyGain: averageDailyGain,
      totalRecords: filteredRecords.length,
      dateRange: dateRange,
      weightTrend: weightTrend,
    );
  }



  /// Filter records by date range - extracted helper method
  static List<WeightRecordIsar> _filterRecordsByDateRange(
    List<WeightRecordIsar> records,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    if (startDate == null || endDate == null) return records;

    final inclusiveEndDate = endDate.add(const Duration(days: 1));
    return records.where((record) {
      final date = record.measurementDate;
      if (date == null) return false;
      return !date.isBefore(startDate) && date.isBefore(inclusiveEndDate);
    }).toList();
  }

  /// Sort records by date - extracted helper method
  static List<WeightRecordIsar> _sortRecordsByDate(List<WeightRecordIsar> records) {
    return List<WeightRecordIsar>.from(records)
      ..sort((a, b) => (a.measurementDate ?? DateTime.now())
          .compareTo(b.measurementDate ?? DateTime.now()));
  }

  /// Calculate total weight gain - extracted helper method
  static double _calculateTotalWeightGain(List<WeightRecordIsar> sortedRecords) {
    if (sortedRecords.length < 2) return 0.0;

    final firstWeight = sortedRecords.first.weight;
    final lastWeight = sortedRecords.last.weight;
    return lastWeight - firstWeight;
  }

  /// Format date range for display - extracted helper method
  static String _formatDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate != null && endDate != null) {
      return '${_formatDate(startDate)} - ${_formatDate(endDate)}';
    } else {
      return 'All time';
    }
  }

  /// Helper method to format dates
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Private accumulator class for single-pass analytics processing
/// Eliminates redundant calculations by processing each record once
class _WeightAnalyticsAccumulator {
  int totalRecords = 0;
  double totalWeight = 0.0;
  DateTime? earliestDate;
  DateTime? latestDate;
  final Set<int> uniqueCattleIds = <int>{};

  /// Process a single weight record - updates all metrics in one pass
  void process(WeightRecordIsar record) {
    totalRecords++;
    totalWeight += record.weight;

    // Track unique cattle
    final cattleId = record.cattle.value?.id;
    if (cattleId != null) {
      uniqueCattleIds.add(cattleId);
    }

    // Track date range
    final measurementDate = record.measurementDate;
    if (measurementDate != null) {
      if (earliestDate == null || measurementDate.isBefore(earliestDate!)) {
        earliestDate = measurementDate;
      }
      if (latestDate == null || measurementDate.isAfter(latestDate!)) {
        latestDate = measurementDate;
      }
    }
  }

  /// Convert accumulated data to AnalyticsSummary result
  AnalyticsSummary toSummary() {
    return AnalyticsSummary(
      totalRecords: totalRecords,
      totalCattle: uniqueCattleIds.length,
      averageWeight: totalRecords > 0 ? totalWeight / totalRecords : 0.0,
      totalWeight: totalWeight,
      earliestRecord: earliestDate,
      latestRecord: latestDate,
    );
  }
}
