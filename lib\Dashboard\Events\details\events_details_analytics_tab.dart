import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/events_details_controller.dart';
import '../models/event_isar.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../widgets/index.dart';


class EventsDetailsAnalyticsTab extends StatelessWidget {
  const EventsDetailsAnalyticsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EventsDetailsController>(
      builder: (context, controller, child) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        if (controller.events.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Events Analytics',
            message: 'Add events for ${controller.cattle?.name ?? 'this cattle'} to see analytics.',
            tabColor: AppColors.eventsKpiColors[0],
            tabIndex: 0,

          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Events Summary Cards
              _buildSummaryCards(controller),
              const SizedBox(height: 24),
              
              // Event Type Distribution
              _buildEventTypeDistribution(context, controller),
              const SizedBox(height: 24),

              // Priority Distribution
              _buildPriorityDistribution(context, controller),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(EventsDetailsController controller) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        UniversalInfoCard(
          title: 'Total Events',
          value: controller.totalEvents.toString(),
          subtitle: 'All events',
          icon: Icons.event,
          color: AppColors.eventsKpiColors[0],
        ),
        UniversalInfoCard(
          title: 'Upcoming',
          value: controller.upcomingEvents.toString(),
          subtitle: 'Future events',
          icon: Icons.schedule,
          color: AppColors.eventsKpiColors[1],
        ),
        UniversalInfoCard(
          title: 'Completed',
          value: controller.completedEvents.toString(),
          subtitle: 'Finished events',
          icon: Icons.check_circle,
          color: AppColors.eventsKpiColors[2],
        ),
        UniversalInfoCard(
          title: 'Overdue',
          value: controller.overdueEvents.toString(),
          subtitle: 'Past due events',
          icon: Icons.warning,
          color: Colors.red,
        ),
      ],
    );
  }

  Widget _buildEventTypeDistribution(BuildContext context, EventsDetailsController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Event Type Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...controller.eventTypeDistribution.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(entry.key.name),
                    ),
                    Text(
                      entry.value.toString(),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityDistribution(BuildContext context, EventsDetailsController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Priority Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...controller.priorityDistribution.entries.map((entry) {
              Color priorityColor;
              switch (entry.key) {
                case EventPriority.high:
                  priorityColor = Colors.red;
                  break;
                case EventPriority.medium:
                  priorityColor = Colors.orange;
                  break;
                case EventPriority.low:
                  priorityColor = Colors.green;
                  break;
              }
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: priorityColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(entry.key.name.toUpperCase()),
                    ),
                    Text(
                      entry.value.toString(),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}
