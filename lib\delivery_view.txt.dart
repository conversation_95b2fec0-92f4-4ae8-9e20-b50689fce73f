// LEGACY FILE - COMMENTED OUT FOR ANALYSIS PURPOSES
// This file contains legacy delivery implementation for reference
// DO NOT UNCOMMENT - Use for feature analysis only

/*
import 'package:flutter/material.dart';
import 'dart:async';
import '../../models/cattle_isar.dart';
import 'package:cattle_manager/services/database/database_helper.dart';
import '../../../../Dashboard/Breeding/dialogs/delivery_form_dialog.dart';
import '../../widgets/delivery_history_card.dart';
import '../../widgets/status_card.dart';
import '../../widgets/stats_card.dart';

class DeliveryView extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar) onCattleUpdated;

  const DeliveryView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
  });

  @override
  State<DeliveryView> createState() => _DeliveryViewState();
}

class _DeliveryViewState extends State<DeliveryView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  bool _isLoading = true;
  List<Map<String, dynamic>> _deliveryRecords = [];
  StreamSubscription? _breedingRecordSubscription;
  StreamSubscription? _pregnancyRecordSubscription;
  bool _isProcessingState = false;
  Timer? _stateDebounceTimer;
  List<CattleIsar> _allCattle = [];

  @override
  void initState() {
    super.initState();
    _loadDeliveryRecords();
    _subscribeToRecordUpdates();
  }

  @override
  void dispose() {
    _breedingRecordSubscription?.cancel();
    _pregnancyRecordSubscription?.cancel();
    _stateDebounceTimer?.cancel();
    super.dispose();
  }

  void _subscribeToRecordUpdates() {
    Timer? debounceTimer;
    Map<String, dynamic>? lastUpdate;

    // Listen to breeding record updates
    _breedingRecordSubscription =
        _databaseHelper.breedingRecordStream.listen((event) async {
      _handleRecordUpdate(event, debounceTimer, lastUpdate);
    });

    // Listen to pregnancy record updates
    _pregnancyRecordSubscription =
        _databaseHelper.deliveryRecordStream.listen((event) async {
      _handleRecordUpdate(event, debounceTimer, lastUpdate);
    });
  }

  void _handleRecordUpdate(Map<String, dynamic> event, Timer? debounceTimer,
      Map<String, dynamic>? lastUpdate) {
    final eventCattleId = event['cattleId'];
    final action = event['action'];

    // Check if this event is relevant to our cattle
    if (eventCattleId == widget.cattle.tagId) {
      debugPrint('Record update received: $action for cattle $eventCattleId');

      // Cancel any pending debounce timer
      debounceTimer?.cancel();
      _stateDebounceTimer?.cancel();

      // Set a new debounce timer
      _stateDebounceTimer = Timer(const Duration(milliseconds: 2000), () async {
        if (!mounted || _isProcessingState) return;

        try {
          _isProcessingState = true;

          // Always fetch fresh cattle data
          final updatedCattle = await _databaseHelper.cattleHandler.getCattleByTagId(widget.cattle.tagId ?? '');
          if (!mounted) return;

          if (updatedCattle != null) {
            // Update the widget's cattle data through the callback
            widget.onCattleUpdated(updatedCattle);

            // Force UI refresh with setState
            setState(() {
              // The build method will use the updated cattle data
            });
          }
        } finally {
          _isProcessingState = false;
        }
      });
    }
  }

  @override
  void didUpdateWidget(DeliveryView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Refresh when cattle changes or when important dates change
    if (oldWidget.cattle != widget.cattle ||
        oldWidget.cattle.breedingStatus?.expectedCalvingDate !=
            widget.cattle.breedingStatus?.expectedCalvingDate ||
        oldWidget.cattle.breedingStatus?.breedingDate != 
            widget.cattle.breedingStatus?.breedingDate) {
      setState(() {
        // The build method will use the updated cattle data
      });
    }
  }

  Future<void> _loadDeliveryRecords() async {
    setState(() => _isLoading = true);
    try {
      _deliveryRecords = await _databaseHelper.breedingHandler.getDeliveryRecordsForCattle(widget.cattle.tagId ?? '');
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      _allCattle = allCattle.cast<CattleIsar>();
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading delivery records: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      _deliveryRecords = [];
    }
    setState(() => _isLoading = false);
  }

  Widget _buildStatsCard() {
    // Calculate statistics from delivery records
    int totalDeliveries = _deliveryRecords.length;
    int successfulDeliveries =
        _deliveryRecords.where((r) => r['complications'] == false).length;

    // Calculate total calves and gender breakdown
    int totalCalves = 0;
    int maleCalves = 0;
    int femaleCalves = 0;

    for (var record in _deliveryRecords) {
      if (record['calfDetails'] != null) {
        List<dynamic> calfDetails = record['calfDetails'];
        totalCalves += calfDetails.length;

        for (var calf in calfDetails) {
          if (calf['gender'] == 'Male') {
            maleCalves++;
          } else if (calf['gender'] == 'Female') {
            femaleCalves++;
          }
        }
      } else {
        // If calfDetails is not available, use liveCalves count
        totalCalves += (record['liveCalves'] as int? ?? 0);
      }
    }

    // Use the factory method instead of manually creating the card
    return StatsCard.deliveryStats(
      totalCalves: totalCalves,
      maleCalves: maleCalves,
      femaleCalves: femaleCalves,
      totalDeliveries: totalDeliveries,
      successfulDeliveries: successfulDeliveries,
      onInfoTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Shows detailed information about calf statistics'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      onCardTap: () {
        // Optional: Navigate to detailed delivery records view
      },
    );
  }

  Widget _buildStatusCard() {
    return StatusCard.delivery(
      isPregnant: widget.cattle.breedingStatus?.isPregnant == true,
      dueDate: widget.cattle.breedingStatus?.expectedCalvingDate,
      startDate: widget.cattle.breedingStatus?.breedingDate,
      baseColor: const Color(0xFF2E7D32), // Deep Green
      customStatusText:
          widget.cattle.breedingStatus?.isPregnant != true ? 'Not Available' : null,
      customGuidance: widget.cattle.breedingStatus?.isPregnant != true
          ? 'Cattle is not pregnant. No delivery information available.'
          : null,
      trailing: IconButton(
        icon: const Icon(Icons.info_outline, color: Color(0xFF2E7D32)),
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Delivery Status Information'),
              content: const Text(
                  'This card shows the current delivery status and expected calving date for this cattle.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        },
      ),
      actionButtons: null,
    );
  }

  Widget _buildHistoryCard() {
    // Use a stable key string
    final stableKeyString =
        'delivery_history_${widget.cattle.id}_${DateTime.now().millisecondsSinceEpoch}';

    return FutureBuilder<List<Map<String, dynamic>>>(
      key: ValueKey(stableKeyString),
      future: _databaseHelper.breedingHandler.getDeliveryRecordsForCattle(widget.cattle.tagId ?? ''),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Card(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error loading delivery records: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          );
        }

        final deliveryRecords = snapshot.data ?? [];

        // Sort by date, most recent first
        deliveryRecords.sort((a, b) {
          final dateA = DateTime.parse(a['date']);
          final dateB = DateTime.parse(b['date']);
          return dateB.compareTo(dateA);
        });

        // Add cattleName and cattleId to each record for display
        for (final record in deliveryRecords) {
          record['cattleName'] = widget.cattle.name;
          record['cattleId'] = widget.cattle.tagId;
        }

        return DeliveryHistoryCard(
          records: deliveryRecords,
          title: 'Delivery History',
          emptyMessage: 'No delivery records found',
          onEdit: _editDeliveryRecord,
          onDelete: _deleteDeliveryRecord,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDeliveryRecords,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildStatsCard(),
                    const SizedBox(height: 16),
                    _buildStatusCard(),
                    const SizedBox(height: 16),
                    _buildHistoryCard(),
                    // Add bottom padding for better spacing with FAB
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            ),
      floatingActionButton:
          widget.cattle.breedingStatus?.isPregnant == true ? _shouldShowDeliveryFAB() : null,
    );
  }

  // Helper method to determine if the FAB should be shown
  Widget? _shouldShowDeliveryFAB() {
    // If no expected calving date, don't show FAB
    if (widget.cattle.breedingStatus?.expectedCalvingDate == null) {
      return null;
    }

    final daysUntilDue =
        widget.cattle.breedingStatus!.expectedCalvingDate!.difference(DateTime.now()).inDays;

    // Option 1: Hide FAB completely if more than 30 days away
    // if (daysUntilDue > 30) {
    //   return null;
    // }

    // Option 2: Show FAB but with different behavior based on days remaining
    return FloatingActionButton(
      onPressed: () {
        if (daysUntilDue > 30) {
          // Show warning dialog when trying to record delivery too early
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              icon: const Icon(Icons.warning_amber_rounded,
                  color: Color(0xFFE53935), size: 40),
              title: const Text(
                'WARNING: EARLY DELIVERY',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFE53935),
                ),
                textAlign: TextAlign.center,
              ),
              content: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                  ),
                  children: [
                    const TextSpan(
                      text: 'The expected calving date is still ',
                    ),
                    TextSpan(
                      text: '$daysUntilDue days away',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFFE53935),
                      ),
                    ),
                    const TextSpan(
                      text:
                          '.\n\nAre you sure you want to record a delivery at this time?',
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('CANCEL',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showDeliveryForm(context);
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFFE53935),
                  ),
                  child: const Text('PROCEED ANYWAY',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
          );
        } else {
          // Within 30 days, proceed directly to form
          _showDeliveryForm(context);
        }
      },
      backgroundColor: _getDueDateColor(),
      tooltip: 'Record Delivery',
      child: const Icon(Icons.add),
    );
  }

  // Helper method to determine FAB color based on due date
  Color _getDueDateColor() {
    // Use the same color (Deep Green) for all states
    return const Color(0xFF2E7D32);
  }

  void _showDeliveryForm(BuildContext context) async {
    if (!mounted) return;
    final initialContext = context;
    
    try {
      setState(() => _isLoading = true);

      // We don't need to reassign _allCattle here as it's already loaded in _loadDeliveryRecords

      if (!mounted) return;
      setState(() => _isLoading = false);

      if (!mounted || !initialContext.mounted) return;
      final result = await showDialog<Map<String, dynamic>>(
        context: initialContext,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) => DeliveryFormDialog(
          motherTagId: widget.cattle.tagId ?? '',
          existingCattle: _allCattle,
        ),
      );

      if (result != null && mounted) {
        // Process the delivery result
        try {
          // Save new calves to database
          if (result['newCalves'] != null) {
            for (var calf in result['newCalves']) {
              final updatedCalf = CattleIsar.fromMap(calf.toMap());
              await _databaseHelper.cattleHandler.updateCattle(updatedCalf);
            }
          }

          // Update mother's pregnancy status by creating a new BreedingStatus
          BreedingStatus updatedBreedingStatus;
          if (widget.cattle.breedingStatus != null) {
            updatedBreedingStatus = BreedingStatus();
            updatedBreedingStatus.status = widget.cattle.breedingStatus!.status;
            updatedBreedingStatus.lastHeatDate = widget.cattle.breedingStatus!.lastHeatDate;
            updatedBreedingStatus.isPregnant = false;
            updatedBreedingStatus.breedingDate = widget.cattle.breedingStatus!.breedingDate;
            updatedBreedingStatus.expectedCalvingDate = null;
            updatedBreedingStatus.nextHeatDate = widget.cattle.breedingStatus!.nextHeatDate;
            updatedBreedingStatus.lastBreedingMethod = widget.cattle.breedingStatus!.lastBreedingMethod;
            updatedBreedingStatus.lastCalvingDate = widget.cattle.breedingStatus!.lastCalvingDate;
          } else {
            updatedBreedingStatus = BreedingStatus();
            updatedBreedingStatus.isPregnant = false;
          }

          final updatedCattle = widget.cattle.copyWith(
            breedingStatus: updatedBreedingStatus,
          );
          await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

          if (!mounted || !initialContext.mounted) return;
          ScaffoldMessenger.of(initialContext).showSnackBar(
            const SnackBar(content: Text('Delivery recorded successfully')),
          );
          setState(() {
            widget.onCattleUpdated(updatedCattle);
          });
        } catch (e) {
          if (!mounted || !initialContext.mounted) return;
          ScaffoldMessenger.of(initialContext).showSnackBar(
            SnackBar(content: Text('Error recording delivery: $e')),
          );
        }
      }
    } catch (e) {
      if (!mounted || !initialContext.mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(initialContext).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  // Method to edit a delivery record
  Future<void> _editDeliveryRecord(Map<String, dynamic> record) async {
    if (!mounted) return;
    
    try {
      setState(() => _isLoading = true);

      // Show the delivery form dialog with the current record data
      if (!mounted) return;
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (dialogContext) => DeliveryFormDialog(
          motherTagId: widget.cattle.tagId ?? '',
          existingCattle: _allCattle,
          record: record,
        ),
      );

      // If the user submitted the form
      if (result != null && mounted) {
        // Preserve the record ID
        final updatedRecord = Map<String, dynamic>.from(result);
        updatedRecord['id'] = record['id'];

        // Update the delivery record
        await _databaseHelper.breedingHandler.updateDeliveryRecordFromMap(updatedRecord);

        // Refresh the data
        await _loadDeliveryRecords();

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Delivery record updated successfully'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating delivery record: $e')),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Method to delete a delivery record
  Future<void> _deleteDeliveryRecord(Map<String, dynamic> record) async {
    if (!mounted) return;
    
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: const Text(
          'Are you sure you want to delete this delivery record? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            style: TextButton.styleFrom(
                foregroundColor: const Color(0xFFE53935)), // Red
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        setState(() => _isLoading = true);

        // Delete the delivery record
        await _databaseHelper.breedingHandler.deleteDeliveryRecord(record['id']);

        // Refresh the data
        await _loadDeliveryRecords();

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Delivery record deleted successfully'),
            backgroundColor: Color(0xFFE53935), // Red
          ),
        );
      } catch (e) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting delivery record: $e')),
        );
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }
}
*/

// END OF LEGACY DELIVERY VIEW FILE
