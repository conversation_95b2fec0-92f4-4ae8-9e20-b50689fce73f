import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../controllers/transaction_controller.dart';
import '../../widgets/index.dart';

/// Data class for analytics info cards to provide type safety
class AnalyticsCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String? insight;

  const AnalyticsCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.insight,
  });
}

class TransactionAnalyticsTab extends StatefulWidget {
  final TransactionController controller;

  const TransactionAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<TransactionAnalyticsTab> createState() => _TransactionAnalyticsTabState();
}

class _TransactionAnalyticsTabState extends State<TransactionAnalyticsTab>
    with AutomaticKeepAliveClientMixin {
  int _selectedChartIndex = 0; // 0: Category Breakdown, 1: Payment Methods

  // Use global constants instead of local ones
  static const _fallbackColor = AppColors.fallback;

  // Common chart configuration
  static const _chartRadius = 80.0;
  static const _chartCenterSpace = 50.0;
  static const _chartSectionsSpace = 2.0;
  static const _chartHeight = 240.0;

  // Common text styles
  static const _chartTitleStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  late final FilterController _filterController;

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
  }

  @override
  void dispose() {
    _filterController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;



  // Universal pie chart builder - pure UI component without business logic
  Widget _buildUniversalPieChart(Map<String, dynamic> data, Map<String, Color> colors, {String? emptyMessage}) {
    if (data.isEmpty || widget.controller.totalTransactions == 0) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    // Ensure we have valid data before rendering
    final validEntries = data.entries.where((entry) => (entry.value as num) > 0).toList();
    if (validEntries.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    final total = validEntries.fold<double>(0, (sum, entry) => sum + (entry.value as num).toDouble());
    final sections = validEntries.map((entry) {
      final value = (entry.value as num).toDouble();
      final percentage = (value / total) * 100;
      final sectionColor = colors[entry.key] ?? _fallbackColor;

      return PieChartSectionData(
        color: sectionColor,
        value: value,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: _chartRadius,
        titleStyle: _chartTitleStyle,
      );
    }).toList();

    // Add a small delay to ensure smooth rendering
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: _chartCenterSpace,
          sectionsSpace: _chartSectionsSpace,
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (widget.controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (widget.controller.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Error Loading Data',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.controller.errorMessage ?? 'Failed to load analytics data',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => widget.controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        // Check if we have data to display
        if (widget.controller.totalTransactions == 0) {
          return UniversalTabEmptyState.forTab(
            title: 'No Transaction Data',
            message: 'Add transactions to your records to view comprehensive analytics and insights.',
            tabColor: AppColors.transactionHeader,
            tabIndex: 0, // Analytics tab
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () {
                // Navigate to add transaction screen
                Navigator.of(context).pushNamed('/transactions/add');
              },
              tabColor: AppColors.transactionHeader,
            ),
          );
        }

        // Define sections for clean, declarative layout
        final sections = [
          _buildEnhancedKPIDashboard(context),
          _buildTransactionCompositionAnalytics(context),
          _buildFinancialAnalytics(context),
          _buildPerformanceOverview(context),
        ];

        // Use RefreshIndicator with SingleChildScrollView for pull-to-refresh functionality
        return RefreshIndicator(
          onRefresh: () async {
            await widget.controller.refresh();
          },
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(kPaddingMedium), // Use global constant
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (int i = 0; i < sections.length; i++) ...[
                  sections[i],
                  if (i < sections.length - 1) const SizedBox(height: kSpacingLarge), // Use global constant
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedKPIDashboard(BuildContext context) {
    final cardData = _buildKPICards();
    return _buildAnalyticsSection(
      title: 'Key Performance Indicators',
      subtitle: 'Essential metrics for your financial management',
      icon: Icons.dashboard_outlined,
      headerColor: AppColors.transactionHeader,
      cardData: cardData,
    );
  }

  /// Build KPI cards using AnalyticsCardData for better type safety
  List<AnalyticsCardData> _buildKPICards() {
    final analyticsSummary = widget.controller.analyticsSummary;

    return [
      AnalyticsCardData(
        title: 'Total Transactions',
        value: analyticsSummary.totalTransactions.toString(),
        subtitle: 'total records',
        icon: Icons.receipt_long,
        color: AppColors.transactionKpiColors[0],
        insight: 'All transaction entries',
      ),
      AnalyticsCardData(
        title: 'Total Income',
        value: '\$${analyticsSummary.totalIncome.toStringAsFixed(2)}',
        subtitle: 'revenue generated',
        icon: Icons.arrow_upward,
        color: AppColors.transactionKpiColors[1],
        insight: 'Total earnings',
      ),
      AnalyticsCardData(
        title: 'Total Expenses',
        value: '\$${analyticsSummary.totalExpenses.toStringAsFixed(2)}',
        subtitle: 'costs incurred',
        icon: Icons.arrow_downward,
        color: AppColors.transactionKpiColors[2],
        insight: 'Total spending',
      ),
      AnalyticsCardData(
        title: 'Net Balance',
        value: '\$${analyticsSummary.netBalance.toStringAsFixed(2)}',
        subtitle: 'profit/loss',
        icon: Icons.account_balance_wallet,
        color: AppColors.transactionKpiColors[3],
        insight: analyticsSummary.netBalance >= 0 ? 'Profitable' : 'Loss',
      ),
      AnalyticsCardData(
        title: 'Avg Transaction',
        value: '\$${analyticsSummary.averageTransactionAmount.toStringAsFixed(2)}',
        subtitle: 'average amount',
        icon: Icons.calculate,
        color: AppColors.transactionKpiColors[4],
        insight: 'Transaction size',
      ),
      AnalyticsCardData(
        title: 'Savings Rate',
        value: '${analyticsSummary.savingsRate.toStringAsFixed(1)}%',
        subtitle: 'savings percentage',
        icon: Icons.savings,
        color: AppColors.transactionKpiColors[5],
        insight: 'Financial health',
      ),
    ];
  }

  Widget _buildTransactionCompositionAnalytics(BuildContext context) {
    final categoryData = widget.controller.categoryBreakdown;
    final paymentMethodData = widget.controller.paymentMethodBreakdown;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Transaction Composition Analytics',
          icon: Icons.pie_chart_outline,
          color: AppColors.transactionHeader,
          subtitle: 'Detailed breakdown of transaction categories and payment methods',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Toggle buttons for chart selection
        _buildChartToggleButtons(context),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Single chart display based on selection
        _buildSelectedChart(context, categoryData, paymentMethodData),
      ],
    );
  }

  Widget _buildChartToggleButtons(BuildContext context) {
    final chartOptions = [
      {'title': 'Categories', 'icon': Icons.category},
      {'title': 'Payment Methods', 'icon': Icons.payment},
    ];

    // Use colors from AppColors instead of hardcoded values
    final toggleColors = [
      AppColors.transactionKpiColors[0], // Pink
      AppColors.transactionKpiColors[1], // Green
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chartOptions.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedChartIndex == index;

        return ChoiceChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected ? Colors.white : toggleColors[index],
              ),
              const SizedBox(width: 6),
              Text(
                option['title'] as String,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: isSelected ? Colors.white : toggleColors[index],
                ),
              ),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedChartIndex = index;
              });
            }
          },
          selectedColor: toggleColors[index],
          backgroundColor: Colors.white,
          side: BorderSide(
            color: toggleColors[index],
            width: 2,
          ),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        );
      }).toList(),
    );
  }

  Widget _buildSelectedChart(
    BuildContext context,
    Map<String, double> categoryData,
    Map<String, double> paymentMethodData,
  ) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildEnhancedChart(
          context,
          'Category Distribution',
          _buildUniversalPieChart(categoryData, _getCategoryColors()),
          _buildEnhancedLegend(categoryData, _getCategoryColors()),
          Icons.category,
        );
      case 1:
        return _buildEnhancedChart(
          context,
          'Payment Method Distribution',
          _buildUniversalPieChart(paymentMethodData, _getPaymentMethodColors()),
          _buildEnhancedLegend(paymentMethodData, _getPaymentMethodColors()),
          Icons.payment,
        );
      default:
        return _buildEnhancedChart(
          context,
          'Category Distribution',
          _buildUniversalPieChart(categoryData, _getCategoryColors()),
          _buildEnhancedLegend(categoryData, _getCategoryColors()),
          Icons.category,
        );
    }
  }

  Widget _buildEnhancedChart(
    BuildContext context,
    String title,
    Widget chart,
    Widget? legend,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(kBorderRadius * 2), // Use global constant with multiplier
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingLarge), // Use global constant
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.transactionHeader.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.transactionHeader,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingLarge), // Use global constant
            SizedBox(height: _chartHeight, child: Center(child: chart)), // Chart height and centered
            if (legend != null) ...[
              const SizedBox(height: kSpacingMedium), // Use global constant
              legend,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialAnalytics(BuildContext context) {
    final cardData = _buildFinancialCards();
    return _buildAnalyticsSection(
      title: 'Financial Analytics',
      subtitle: 'Detailed financial performance and efficiency metrics',
      icon: Icons.analytics,
      headerColor: AppColors.transactionKpiColors[1],
      cardData: cardData,
    );
  }

  /// Build financial analytics cards using AnalyticsCardData for better type safety
  List<AnalyticsCardData> _buildFinancialCards() {
    final analyticsSummary = widget.controller.analyticsSummary;

    return [
      AnalyticsCardData(
        title: 'Income/Expense Ratio',
        value: '${analyticsSummary.incomeToExpenseRatio.toStringAsFixed(2)}:1',
        subtitle: 'income ratio',
        icon: Icons.compare_arrows,
        color: AppColors.transactionKpiColors[0],
        insight: 'Financial efficiency',
      ),
      AnalyticsCardData(
        title: 'Top Income Category',
        value: analyticsSummary.topIncomeCategory.isNotEmpty
            ? analyticsSummary.topIncomeCategory
            : 'N/A',
        subtitle: 'Best revenue source',
        icon: Icons.trending_up,
        color: AppColors.transactionKpiColors[1],
        insight: 'Best revenue source',
      ),
      AnalyticsCardData(
        title: 'Top Expense Category',
        value: analyticsSummary.topExpenseCategory.isNotEmpty
            ? analyticsSummary.topExpenseCategory
            : 'N/A',
        subtitle: 'Highest cost area',
        icon: Icons.trending_down,
        color: AppColors.transactionKpiColors[2],
        insight: 'Highest cost area',
      ),
      AnalyticsCardData(
        title: 'Transaction Days',
        value: analyticsSummary.daysWithTransactions.toString(),
        subtitle: 'active days',
        icon: Icons.calendar_today,
        color: AppColors.transactionKpiColors[3],
        insight: 'Activity frequency',
      ),
    ];
  }

  Widget _buildPerformanceOverview(BuildContext context) {
    final cardData = _buildPerformanceCards();
    return _buildAnalyticsSection(
      title: 'Performance Overview',
      subtitle: 'Overall financial performance and insights',
      icon: Icons.assessment,
      headerColor: AppColors.transactionKpiColors[3],
      cardData: cardData,
    );
  }

  /// Build performance cards
  List<AnalyticsCardData> _buildPerformanceCards() {
    final analyticsSummary = widget.controller.analyticsSummary;

    return [
      AnalyticsCardData(
        title: 'Average Income',
        value: '\$${analyticsSummary.averageIncome.toStringAsFixed(2)}',
        subtitle: 'per income transaction',
        icon: Icons.attach_money,
        color: AppColors.transactionKpiColors[0],
        insight: 'Income efficiency',
      ),
      AnalyticsCardData(
        title: 'Average Expense',
        value: '\$${analyticsSummary.averageExpense.toStringAsFixed(2)}',
        subtitle: 'per expense transaction',
        icon: Icons.money_off,
        color: AppColors.transactionKpiColors[1],
        insight: 'Spending pattern',
      ),
      AnalyticsCardData(
        title: 'Preferred Payment',
        value: analyticsSummary.mostUsedPaymentMethod.isNotEmpty
            ? analyticsSummary.mostUsedPaymentMethod
            : 'N/A',
        subtitle: 'Most used method',
        icon: Icons.payment,
        color: AppColors.transactionKpiColors[2],
        insight: 'Payment preference',
      ),
      AnalyticsCardData(
        title: 'Financial Health',
        value: analyticsSummary.savingsRate >= 20 ? 'Excellent' :
               analyticsSummary.savingsRate >= 10 ? 'Good' :
               analyticsSummary.savingsRate >= 0 ? 'Fair' : 'Poor',
        subtitle: '${analyticsSummary.savingsRate.toStringAsFixed(1)}% savings',
        icon: Icons.health_and_safety,
        color: AppColors.transactionKpiColors[3],
        insight: 'Overall status',
      ),
    ];
  }

  Widget _buildEnhancedLegend(Map<String, dynamic> data, Map<String, Color> colors) {
    if (data.isEmpty) return const SizedBox.shrink();

    final total = data.values.fold<double>(0, (sum, value) => sum + (value as num).toDouble());

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: data.entries.map((entry) {
        final value = (entry.value as num).toDouble();
        final percentage = total > 0 ? ((value / total) * 100).toStringAsFixed(1) : '0';
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: colors[entry.key]?.withValues(alpha: 0.1) ?? _fallbackColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: colors[entry.key] ?? _fallbackColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '${entry.key} (\$${value.toStringAsFixed(0)}) $percentage%',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }



  Map<String, Color> _getCategoryColors() {
    final categoryData = widget.controller.categoryBreakdown;
    final categoryColors = <String, Color>{};

    // Use colors from AppColors instead of hardcoded values
    const availableColors = AppColors.transactionKpiColors;

    int colorIndex = 0;
    for (final categoryName in categoryData.keys) {
      // Try to use predefined category colors first
      if (AppColors.transactionCategoryColors.containsKey(categoryName)) {
        categoryColors[categoryName] = AppColors.transactionCategoryColors[categoryName]!;
      } else {
        categoryColors[categoryName] = availableColors[colorIndex % availableColors.length];
        colorIndex++;
      }
    }

    return categoryColors;
  }

  Map<String, Color> _getPaymentMethodColors() {
    final paymentMethodData = widget.controller.paymentMethodBreakdown;
    final paymentMethodColors = <String, Color>{};

    // Use colors from AppColors instead of hardcoded values
    const availableColors = AppColors.transactionKpiColors;

    int colorIndex = 0;
    for (final methodName in paymentMethodData.keys) {
      // Try to use predefined payment method colors first
      if (AppColors.transactionPaymentMethodColors.containsKey(methodName)) {
        paymentMethodColors[methodName] = AppColors.transactionPaymentMethodColors[methodName]!;
      } else {
        paymentMethodColors[methodName] = availableColors[colorIndex % availableColors.length];
        colorIndex++;
      }
    }

    return paymentMethodColors;
  }

  /// Build analytics section with consistent styling following cattle module pattern
  Widget _buildAnalyticsSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<AnalyticsCardData> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        ResponsiveGrid.cards(
          children: cardData.map((data) => _buildMetricCard(data)).toList(),
        ),
      ],
    );
  }

  /// Build individual metric card from typed data
  Widget _buildMetricCard(AnalyticsCardData data) {
    return UniversalInfoCard(
      title: data.title,
      value: data.value,
      subtitle: data.subtitle,
      icon: data.icon,
      color: data.color,
      insight: data.insight,
    );
  }
}
