import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../services/health_repository.dart';


/// Controller for managing health details screen state and data
/// Following breeding details controller pattern for real-time synchronization
class HealthDetailsController extends ChangeNotifier {
  final HealthRepository _healthRepository = GetIt.instance<HealthRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  bool _isLoading = false;
  String? _error;
  CattleIsar? _cattle;

  // Health data
  List<HealthRecordIsar> _healthRecords = [];
  List<TreatmentIsar> _treatmentRecords = [];
  List<VaccinationIsar> _vaccinationRecords = [];

  // Real-time synchronization using individual streams (following breeding details pattern)
  StreamSubscription<List<HealthRecordIsar>>? _healthRecordsStreamSubscription;
  StreamSubscription<List<TreatmentIsar>>? _treatmentStreamSubscription;
  StreamSubscription<List<VaccinationIsar>>? _vaccinationStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  CattleIsar? get cattle => _cattle;
  List<HealthRecordIsar> get healthRecords => _healthRecords;
  List<TreatmentIsar> get treatmentRecords => _treatmentRecords;
  List<VaccinationIsar> get vaccinationRecords => _vaccinationRecords;

  // Computed properties for analytics
  int get totalHealthRecords => _healthRecords.length;
  int get totalTreatmentRecords => _treatmentRecords.length;
  int get totalVaccinationRecords => _vaccinationRecords.length;
  int get totalAllRecords => totalHealthRecords + totalTreatmentRecords + totalVaccinationRecords;
  
  List<HealthRecordIsar> get recentRecords {
    final sorted = List<HealthRecordIsar>.from(_healthRecords)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    return sorted.take(5).toList();
  }

  bool get hasActiveIssues {
    return _healthRecords.any((r) => r.status?.toLowerCase() == 'active');
  }

  /// Initialize controller with cattle data and start real-time streams
  void initialize(CattleIsar cattle) {
    _cattle = cattle;
    _initializeStreamListeners();
    // No need for manual data loading - streams will handle it automatically
  }

  /// Initialize real-time stream listeners following breeding details pattern
  /// Using individual streams instead of StreamZip for better reliability
  void _initializeStreamListeners() {
    if (_cattle == null) {
      debugPrint('❌ HEALTH DETAILS: Cannot initialize stream listeners: cattle is null');
      return;
    }

    debugPrint('🔧 HEALTH DETAILS: Initializing individual stream listeners (cattle module pattern)...');
    debugPrint('🔧 HEALTH DETAILS: Cattle: ${_cattle!.tagId} (businessId: ${_cattle!.businessId})');

    // Health records stream - filtered by cattle tagId (health records use tagId in cattleId field)
    _healthRecordsStreamSubscription = _isar.healthRecordIsars
        .filter()
        .cattleIdEqualTo(_cattle!.tagId)
        .watch(fireImmediately: true)
        .listen((healthRecords) {
      debugPrint('🔄 HEALTH RECORDS STREAM: Received ${healthRecords.length} health records');
      _healthRecords = healthRecords;
      _setLoading(false);
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Health records stream error: $error');
      _setError('Failed to load health records: $error');
    });

    // Treatment records stream - filtered by cattle tagId
    _treatmentStreamSubscription = _isar.treatmentIsars
        .filter()
        .cattleIdEqualTo(_cattle!.tagId)
        .watch(fireImmediately: true)
        .listen((treatmentRecords) {
      debugPrint('🔄 TREATMENTS STREAM: Received ${treatmentRecords.length} treatments');
      _treatmentRecords = treatmentRecords;
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Treatment records stream error: $error');
    });

    // Vaccination records stream - filtered by cattle tagId
    _vaccinationStreamSubscription = _isar.vaccinationIsars
        .filter()
        .cattleIdEqualTo(_cattle!.tagId)
        .watch(fireImmediately: true)
        .listen((vaccinationRecords) {
      debugPrint('🔄 VACCINATIONS STREAM: Received ${vaccinationRecords.length} vaccinations');
      _vaccinationRecords = vaccinationRecords;
      notifyListeners();
    }, onError: (error) {
      debugPrint('❌ Vaccination records stream error: $error');
    });

    // Cattle stream - watch for changes to the specific cattle record
    _cattleStreamSubscription = _isar.cattleIsars
        .filter()
        .businessIdEqualTo(_cattle!.businessId)
        .watch(fireImmediately: true)
        .listen((cattleList) {
      debugPrint('🔄 CATTLE STREAM: Received ${cattleList.length} cattle records');
      final cattle = cattleList.isNotEmpty ? cattleList.first : null;
      if (cattle != null) {
        _cattle = cattle;
        notifyListeners();
      }
    }, onError: (error) {
      debugPrint('❌ Cattle stream error: $error');
    });

    debugPrint('✅ HEALTH DETAILS: Individual stream listeners initialized');
  }

  /// Refresh all data (streams handle this automatically, but kept for compatibility)
  Future<void> refresh() async {
    // With real-time streams, data refreshes automatically
    // This method is kept for compatibility but streams handle updates
    debugPrint('🔄 HEALTH DETAILS: Refresh called (streams handle automatic updates)');
  }

  /// Add a new health record (streams will automatically update UI)
  Future<bool> addHealthRecord(HealthRecordIsar record) async {
    try {
      debugPrint('💾 HEALTH DETAILS: Adding health record for cattle ${_cattle?.tagId}');
      await _healthRepository.saveHealthRecord(record);
      // No manual refresh needed - streams will automatically update the UI
      debugPrint('✅ HEALTH DETAILS: Health record added successfully');
      return true;
    } catch (e) {
      debugPrint('❌ HEALTH DETAILS: Failed to add health record: $e');
      _setError('Failed to add health record: $e');
      return false;
    }
  }

  /// Update an existing health record (streams will automatically update UI)
  Future<bool> updateHealthRecord(HealthRecordIsar record) async {
    try {
      debugPrint('💾 HEALTH DETAILS: Updating health record ${record.id}');
      await _healthRepository.saveHealthRecord(record);
      // No manual refresh needed - streams will automatically update the UI
      debugPrint('✅ HEALTH DETAILS: Health record updated successfully');
      return true;
    } catch (e) {
      debugPrint('❌ HEALTH DETAILS: Failed to update health record: $e');
      _setError('Failed to update health record: $e');
      return false;
    }
  }

  /// Delete a health record (streams will automatically update UI)
  Future<bool> deleteHealthRecord(int recordId) async {
    try {
      debugPrint('🗑️ HEALTH DETAILS: Deleting health record $recordId');
      await _healthRepository.deleteHealthRecord(recordId);
      // No manual refresh needed - streams will automatically update the UI
      debugPrint('✅ HEALTH DETAILS: Health record deleted successfully');
      return true;
    } catch (e) {
      debugPrint('❌ HEALTH DETAILS: Failed to delete health record: $e');
      _setError('Failed to delete health record: $e');
      return false;
    }
  }

  /// Add a new treatment record (streams will automatically update UI)
  Future<bool> addTreatmentRecord(TreatmentIsar record) async {
    try {
      debugPrint('💾 HEALTH DETAILS: Adding treatment record for cattle ${_cattle?.tagId}');
      await _healthRepository.saveTreatment(record);
      // No manual refresh needed - streams will automatically update the UI
      debugPrint('✅ HEALTH DETAILS: Treatment record added successfully');
      return true;
    } catch (e) {
      debugPrint('❌ HEALTH DETAILS: Failed to add treatment record: $e');
      _setError('Failed to add treatment record: $e');
      return false;
    }
  }

  /// Add a new vaccination record (streams will automatically update UI)
  Future<bool> addVaccinationRecord(VaccinationIsar record) async {
    try {
      debugPrint('💾 HEALTH DETAILS: Adding vaccination record for cattle ${_cattle?.tagId}');
      await _healthRepository.saveVaccination(record);
      // No manual refresh needed - streams will automatically update the UI
      debugPrint('✅ HEALTH DETAILS: Vaccination record added successfully');
      return true;
    } catch (e) {
      debugPrint('❌ HEALTH DETAILS: Failed to add vaccination record: $e');
      _setError('Failed to add vaccination record: $e');
      return false;
    }
  }

  /// Update an existing vaccination record (streams will automatically update UI)
  Future<bool> updateVaccinationRecord(VaccinationIsar record) async {
    try {
      debugPrint('💾 HEALTH DETAILS: Updating vaccination record ${record.id}');
      await _healthRepository.saveVaccination(record);
      // No manual refresh needed - streams will automatically update the UI
      debugPrint('✅ HEALTH DETAILS: Vaccination record updated successfully');
      return true;
    } catch (e) {
      debugPrint('❌ HEALTH DETAILS: Failed to update vaccination record: $e');
      _setError('Failed to update vaccination record: $e');
      return false;
    }
  }

  /// Delete a vaccination record (streams will automatically update UI)
  Future<bool> deleteVaccinationRecord(int recordId) async {
    try {
      debugPrint('🗑️ HEALTH DETAILS: Deleting vaccination record $recordId');
      await _healthRepository.deleteVaccination(recordId);
      // No manual refresh needed - streams will automatically update the UI
      debugPrint('✅ HEALTH DETAILS: Vaccination record deleted successfully');
      return true;
    } catch (e) {
      debugPrint('❌ HEALTH DETAILS: Failed to delete vaccination record: $e');
      _setError('Failed to delete vaccination record: $e');
      return false;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  @override
  void dispose() {
    debugPrint('🧹 HEALTH DETAILS: Disposing stream subscriptions');
    _healthRecordsStreamSubscription?.cancel();
    _treatmentStreamSubscription?.cancel();
    _vaccinationStreamSubscription?.cancel();
    _cattleStreamSubscription?.cancel();
    super.dispose();
  }
}
