import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';
import '../models/milk_report_data_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';

// --- Constants ---
class _AppStrings {
  static const String exportReportTitle = 'Export Milk Report';
  static const String exportFormatLabel = 'Export Format';
}

class ExportMilkReportDialog extends StatefulWidget {
  final MilkReportDataIsar reportData;

  const ExportMilkReportDialog({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  ExportMilkReportDialogState createState() => ExportMilkReportDialogState();
}

class ExportMilkReportDialogState extends State<ExportMilkReportDialog> {
  bool _includeSummary = true;
  bool _includeChart = true;
  bool _includeDetails = true;
  String _exportFormat = 'PDF';
  bool _isExporting = false;

  @override
  Widget build(BuildContext context) {
    return UniversalFormDialog(
      title: _AppStrings.exportReportTitle,
      headerIcon: Icons.file_download, // Export-specific icon
      formContent: _buildFormContent(),
      actionButtons: UniversalDialogButtons.cancelAddRow(
        onCancel: () => Navigator.of(context).pop(),
        onAdd: _handleExport,
        addText: 'Export', // Export-specific button text
        isAdding: _isExporting,
      ),
    );
  }

  Widget _buildFormContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Include Summary Checkbox
        ListTile(
          leading: const Icon(Icons.summarize, color: Colors.blue),
          title: const Text('Include Summary'),
          trailing: Switch(
            value: _includeSummary,
            onChanged: (value) => setState(() => _includeSummary = value),
          ),
        ),
        UniversalFormField.spacing,

        // Include Chart Checkbox
        ListTile(
          leading: const Icon(Icons.bar_chart, color: Colors.green),
          title: const Text('Include Chart'),
          trailing: Switch(
            value: _includeChart,
            onChanged: (value) => setState(() => _includeChart = value),
          ),
        ),
        UniversalFormField.spacing,

        // Include Details Checkbox
        ListTile(
          leading: const Icon(Icons.list_alt, color: Colors.purple),
          title: const Text('Include Details'),
          trailing: Switch(
            value: _includeDetails,
            onChanged: (value) => setState(() => _includeDetails = value),
          ),
        ),
        UniversalFormField.spacing,

        // Export Format Dropdown
        UniversalFormField.dropdownField<String>(
          label: _AppStrings.exportFormatLabel,
          value: _exportFormat,
          items: const [
            DropdownMenuItem(value: 'PDF', child: Text('PDF')),
            DropdownMenuItem(value: 'Excel', child: Text('Excel')),
          ],
          onChanged: (value) {
            setState(() {
              _exportFormat = value ?? 'PDF';
            });
          },
          prefixIcon: Icons.file_present,
          prefixIconColor: Colors.indigo,
          validator: (value) => UniversalFormField.dropdownValidator(value, 'export format'),
        ),
      ],
    );
  }

  Future<void> _handleExport() async {
    setState(() => _isExporting = true);

    try {
      await _exportReport();

      // Show success message and close dialog
      if (mounted) {
        MessageUtils.showSuccess(context, 'Report exported successfully');
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('ERROR: Failed to export report: $e');
      if (mounted) {
        setState(() => _isExporting = false);
        MessageUtils.showError(context,
            'Error: ${e.toString().replaceAll('Exception: ', '')}');
      }
    }
  }

  Future<void> _exportReport() async {
    final dateFormat = DateFormat('yyyy-MM-dd');

    if (_exportFormat == 'PDF') {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          build: (context) {
            final children = <pw.Widget>[];

            // Title
            children.add(
              pw.Header(
                level: 0,
                child: pw.Text('Milk Production Report',
                    style: pw.TextStyle(
                        fontSize: 24, fontWeight: pw.FontWeight.bold)),
              ),
            );

            // Date Range
            if (widget.reportData.startDate != null ||
                widget.reportData.endDate != null) {
              children.add(
                pw.Paragraph(
                  text:
                      'Period: ${widget.reportData.startDate != null ? dateFormat.format(widget.reportData.startDate!) : 'Start'} to ${widget.reportData.endDate != null ? dateFormat.format(widget.reportData.endDate!) : 'End'}',
                ),
              );
            }

            // Summary Section
            if (_includeSummary) {
              children.add(pw.SizedBox(height: 20));
              children.add(
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius:
                        const pw.BorderRadius.all(pw.Radius.circular(10)),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Summary',
                          style: pw.TextStyle(
                              fontSize: 18, fontWeight: pw.FontWeight.bold)),
                      pw.SizedBox(height: 10),
                      pw.Text('Total Records: ${widget.reportData.totalRecords ?? 0}'),
                      pw.Text('Total Milk: ${widget.reportData.totalMilk ?? 0} L'),
                      pw.Text('Average Daily: ${widget.reportData.averageMilkPerDay ?? 0} L'),
                      if (widget.reportData.topProducerName != null)
                        pw.Text('Top Producer: ${widget.reportData.topProducerName} (${widget.reportData.topProducerAmount ?? 0} L)'),
                    ],
                  ),
                ),
              );
            }

            // Details Section
            if (_includeDetails) {
              children.add(pw.SizedBox(height: 20));
              
              // Since we don't have the actual filteredRecords property in MilkReportDataIsar,
              // we'll just display a placeholder message
              children.add(
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Details',
                          style: pw.TextStyle(
                              fontSize: 18, fontWeight: pw.FontWeight.bold)),
                      pw.SizedBox(height: 10),
                      pw.Text('The detailed milk records are not available for export at this time.'),
                    ],
                  ),
                ),
              );
            }

            return pw.Column(children: children);
          },
        ),
      );

      final tempDir = await getTemporaryDirectory();
      final file = File(
          '${tempDir.path}/milk_production_report_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      if (!mounted) return;

      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'Milk Production Report',
      );
    } else {
      // Generate a CSV file with available data
      final csvData = <List<String>>[
        ['Report Type', 'Total Records', 'Total Milk (L)', 'Average Milk Per Day (L)', 'Top Producer'],
        [
          widget.reportData.reportType ?? 'Milk Report',
          (widget.reportData.totalRecords ?? 0).toString(),
          (widget.reportData.totalMilk ?? 0).toString(),
          (widget.reportData.averageMilkPerDay ?? 0).toString(),
          '${widget.reportData.topProducerName ?? 'N/A'} (${widget.reportData.topProducerAmount ?? 0} L)',
        ],
      ];

      final csvString = csvData.map((row) => row.join(',')).join('\n');
      final tempDir = await getTemporaryDirectory();
      final file = File(
          '${tempDir.path}/milk_production_report_${DateTime.now().millisecondsSinceEpoch}.csv');
      await file.writeAsString(csvString);

      if (!mounted) return;

      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'Milk Production Report',
      );
    }
  }
}
