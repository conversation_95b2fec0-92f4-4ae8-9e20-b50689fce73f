import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/breeding_controller.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';
import '../details/breeding_details_screen.dart';
import '../dialogs/breeding_form_dialog.dart';
import '../models/breeding_record_isar.dart';
import 'package:intl/intl.dart';

class BreedingRecordsTab extends StatefulWidget {
  final BreedingController? controller; // Made optional to support Provider pattern

  const BreedingRecordsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<BreedingRecordsTab> createState() => _BreedingRecordsTabState();
}

class _BreedingRecordsTabState extends State<BreedingRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  BreedingController get _controller => widget.controller ?? context.read<BreedingController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // Apply filters at database level for ultimate scalability
    _controller.applyFilters(_filterController.toBreedingFilterState());
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    debugPrint('📱 BREEDING RECORDS TAB: Building content');
    debugPrint('   Total breeding records: ${_controller.totalBreedingRecords}');
    debugPrint('   Filtered breeding records: ${_controller.breedingRecords.length}');
    debugPrint('   Controller state: ${_controller.state}');

    // Check if we have data to display
    if (_controller.totalBreedingRecords == 0) {
      debugPrint('📱 BREEDING RECORDS TAB: No data - showing empty state');
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    // Get breeding records data - now pre-filtered at database level
    final records = _controller.breedingRecords;
    final allRecordsCount = _controller.totalBreedingRecords; // This represents total before filtering

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.breeding,
          moduleName: 'breeding',
          sortFields: const [...SortField.commonFields, ...SortField.breedingFields],
          searchHint: 'Search breeding records by cattle, method, or status...',
          totalCount: allRecordsCount,
          filteredCount: records.length,
        ),

        // Breeding Records List - data is already filtered at database level
        Expanded(
          child: records.isEmpty
              ? _buildEmptyState(allRecordsCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    // UX Decision: Pull-to-refresh acts as a complete reset, clearing filters
                    // to provide users with a "fresh start" experience. This is intuitive
                    // behavior when users want to see all data without current filter constraints.
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: records.length,
                    itemBuilder: (context, index) {
                      final record = records[index];
                      return _buildBreedingRecordCard(record);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  /// Consolidated empty state builder - single source of truth for all empty states
  ///
  /// [isCompletelyEmpty] - true when no breeding records exist at all, false when records exist but filters hide them
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    // Get the tab color for Records tab (index 1) from breeding module
    const tabColor = AppColors.breedingHeader;

    if (isCompletelyEmpty) {
      // No breeding records exist - show call-to-action to add first record
      return UniversalTabEmptyState.forTab(
        title: 'No Breeding Records',
        message: 'Add your first breeding record to start tracking your herd\'s breeding program.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddBreedingRecordDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Breeding records exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Breeding Records',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildBreedingRecordCard(BreedingRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleId);

    // Format row 1: Date + Method
    String dateText = _formatDate(record.date);
    String methodText = record.method ?? 'Unknown Method';

    // Format row 2: Cattle name and status
    String cattleName = cattle?.name ?? 'Unknown Cattle';
    String statusText = record.status ?? 'Unknown Status';

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: methodText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.science,
      row2Left: cattleName,
      row2Right: statusText,
      row2LeftIcon: Icons.pets,
      row2RightIcon: _getStatusIcon(record.status),
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.breedingHeader,
      onTap: () => _navigateToBreedingRecordDetail(record),
      onEdit: () => _showEditBreedingRecordDialog(record),
      onDelete: () => _showDeleteConfirmation(record),
    );
  }

  void _navigateToBreedingRecordDetail(BreedingRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleId);
    if (cattle == null) return; // Cattle not found, cannot show details

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BreedingDetailsScreen(
          cattle: cattle,
          businessId: cattle.tagId ?? '', // Use cattle's tagId for consistency
          onCattleUpdated: (updatedCattle) {
            // Placeholder for cattle update logic if needed from this screen
          },
        ),
      ),
    );
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Consolidated dialog helper for both add and edit operations
  ///
  /// [record] - null for add operation, existing record for edit operation
  void _showBreedingRecordFormDialog([BreedingRecordIsar? record]) {
    final isEditing = record != null;

    // Real-time check - no artificial delays
    if (_controller.state == ControllerState.loading) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Loading cattle data...'),
          duration: Duration(milliseconds: 800), // Short, real-time feedback
        ),
      );
      return;
    }

    if (_controller.femaleCattleList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No active female cattle available for breeding records.'),
          duration: Duration(milliseconds: 1200), // Quick feedback
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => BreedingFormDialog(
        preloadedCattle: _controller.femaleCattleList,
        preloadedAnimalTypes: _controller.animalTypesMap,
        onSave: (recordData) async {
          if (isEditing) {
            await _controller.updateBreedingRecord(recordData);
          } else {
            await _controller.addBreedingRecord(recordData);
          }
        },
        record: record,
      ),
    );
  }

  /// Show dialog to add new breeding record
  void _showAddBreedingRecordDialog() => _showBreedingRecordFormDialog();

  /// Show dialog to edit existing breeding record
  void _showEditBreedingRecordDialog(BreedingRecordIsar record) => _showBreedingRecordFormDialog(record);

  void _showDeleteConfirmation(BreedingRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleId);
    final recordDescription = 'breeding record for ${cattle?.name ?? 'Unknown cattle'} on ${_formatDate(record.date)}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Breeding Record'),
        content: Text('Are you sure you want to delete this $recordDescription?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _controller.deleteBreedingRecord(record.businessId!);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'completed':
        return Icons.check_circle;
      case 'failed':
        return Icons.cancel;
      case 'pending':
        return Icons.schedule;
      case 'confirmed':
        return Icons.verified;
      default:
        return Icons.help_outline;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}
