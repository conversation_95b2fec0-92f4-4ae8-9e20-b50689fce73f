import 'package:flutter/material.dart';
import '../services/event_analytics_service.dart';

/// Service for generating events management insights and recommendations
/// Extracted from UI layer for better testability and reusability
/// Uses dependency injection pattern for architectural consistency
class EventsInsightsService {
  // Business Rule Thresholds - Centralized for easy maintenance and clarity
  // These constants define the business logic for events management insights

  /// Completion rate below this threshold is considered "low" requiring attention
  static const double _lowCompletionRateThreshold = 0.7; // 70%

  /// Completion rate above this threshold is considered "excellent"
  static const double _excellentCompletionRateThreshold = 0.9; // 90%

  /// Number of events below this threshold is considered "insufficient data"
  static const int _minEventsThreshold = 5;

  /// Number of overdue events above this threshold is considered "high"
  static const int _highOverdueEventsThreshold = 5;

  /// Number of upcoming events above this threshold is considered "busy period"
  static const int _busyPeriodThreshold = 10;

  /// Generate comprehensive insights for events management
  /// This is the main entry point for the insights tab
  List<EventsInsight> generateInsights(EventAnalyticsResult analytics) {
    final insights = <EventsInsight>[];

    // Completion Rate Analysis
    final completionInsight = _analyzeCompletionRate(analytics);
    if (completionInsight != null) {
      insights.add(completionInsight);
    }

    // Overdue Events Analysis
    final overdueInsight = _analyzeOverdueEvents(analytics);
    if (overdueInsight != null) {
      insights.add(overdueInsight);
    }

    // Upcoming Events Analysis
    final upcomingInsight = _analyzeUpcomingEvents(analytics);
    if (upcomingInsight != null) {
      insights.add(upcomingInsight);
    }

    // Event Type Analysis
    final eventTypeInsight = _analyzeEventTypes(analytics);
    if (eventTypeInsight != null) {
      insights.add(eventTypeInsight);
    }

    // General Best Practices (always shown)
    insights.add(_getEventsBestPractices());

    return insights;
  }

  /// Analyze completion rate and provide insights
  EventsInsight? _analyzeCompletionRate(EventAnalyticsResult analytics) {
    if (analytics.totalEvents < _minEventsThreshold) {
      return EventsInsight(
        title: 'Insufficient Event Data',
        description: 'You need more events to analyze completion rates effectively.',
        icon: Icons.info_outline,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Record all farm events consistently',
          'Schedule regular health checks and vaccinations',
          'Track breeding and pregnancy events',
          'Monitor feeding and medication schedules',
        ],
      );
    }

    final completionRate = analytics.completionRate;

    if (completionRate < _lowCompletionRateThreshold) {
      return EventsInsight(
        title: 'Low Event Completion Rate',
        description: 'Your event completion rate of ${(completionRate * 100).toStringAsFixed(1)}% is below optimal levels.',
        icon: Icons.warning,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Review and update pending events regularly',
          'Set up reminders for important events',
          'Prioritize high-priority events first',
          'Consider delegating tasks to team members',
          'Review event scheduling and planning',
        ],
      );
    } else if (completionRate > _excellentCompletionRateThreshold) {
      return EventsInsight(
        title: 'Excellent Event Management',
        description: 'Your event completion rate of ${(completionRate * 100).toStringAsFixed(1)}% is excellent!',
        icon: Icons.star,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current event management practices',
          'Share successful techniques with other farmers',
          'Consider expanding event tracking',
          'Maintain current scheduling standards',
        ],
      );
    }

    return null; // Average completion rate - no specific insight needed
  }

  /// Analyze overdue events and provide insights
  EventsInsight? _analyzeOverdueEvents(EventAnalyticsResult analytics) {
    if (analytics.overdueEvents == 0) {
      return EventsInsight(
        title: 'No Overdue Events',
        description: 'Great job! You have no overdue events. Keep up the excellent time management.',
        icon: Icons.check_circle,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current scheduling practices',
          'Maintain regular event monitoring',
          'Keep up proactive event management',
        ],
      );
    } else if (analytics.overdueEvents > _highOverdueEventsThreshold) {
      return EventsInsight(
        title: 'High Number of Overdue Events',
        description: 'You have ${analytics.overdueEvents} overdue events requiring immediate attention.',
        icon: Icons.schedule,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Address overdue events immediately',
          'Review event scheduling and planning',
          'Set up better reminder systems',
          'Consider prioritizing critical events',
          'Evaluate workload and resource allocation',
        ],
      );
    } else {
      return EventsInsight(
        title: 'Some Overdue Events',
        description: 'You have ${analytics.overdueEvents} overdue events that need attention.',
        icon: Icons.schedule,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Complete overdue events as soon as possible',
          'Review event priorities and deadlines',
          'Improve event scheduling practices',
          'Set up reminders for future events',
        ],
      );
    }
  }

  /// Analyze upcoming events and provide insights
  EventsInsight? _analyzeUpcomingEvents(EventAnalyticsResult analytics) {
    if (analytics.upcomingEvents == 0) {
      return EventsInsight(
        title: 'No Upcoming Events',
        description: 'You have no events scheduled for the next week. Consider planning ahead.',
        icon: Icons.event_available,
        color: Colors.blue,
        priority: InsightPriority.medium,
        recommendations: [
          'Plan and schedule upcoming events',
          'Review cattle health and vaccination schedules',
          'Consider routine maintenance tasks',
          'Schedule regular health checks',
        ],
      );
    } else if (analytics.upcomingEvents > _busyPeriodThreshold) {
      return EventsInsight(
        title: 'Busy Period Ahead',
        description: 'You have ${analytics.upcomingEvents} events scheduled for the next week.',
        icon: Icons.event_busy,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Prepare resources and materials in advance',
          'Review event priorities and schedules',
          'Consider delegating tasks if possible',
          'Ensure adequate time allocation for each event',
        ],
      );
    } else {
      return EventsInsight(
        title: 'Well-Planned Schedule',
        description: 'You have ${analytics.upcomingEvents} events scheduled for the next week.',
        icon: Icons.event,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current planning practices',
          'Prepare for upcoming events',
          'Monitor event progress regularly',
        ],
      );
    }
  }

  /// Analyze event types and provide insights
  EventsInsight? _analyzeEventTypes(EventAnalyticsResult analytics) {
    final eventTypeDistribution = analytics.eventTypeDistribution;
    
    if (eventTypeDistribution.isEmpty) {
      return null;
    }

    final totalEvents = eventTypeDistribution.values.fold(0, (sum, count) => sum + count);
    final mostCommonType = analytics.mostCommonEventType;
    final mostCommonCount = analytics.mostCommonEventTypeCount;

    if (mostCommonType.isNotEmpty && mostCommonCount > 0) {
      final percentage = (mostCommonCount / totalEvents * 100).toStringAsFixed(1);
      
      return EventsInsight(
        title: 'Event Type Distribution',
        description: 'Your most common event type is "$mostCommonType" ($percentage% of all events).',
        icon: Icons.pie_chart,
        color: Colors.purple,
        priority: InsightPriority.low,
        recommendations: [
          'Ensure balanced event scheduling across all types',
          'Consider if this distribution aligns with your farm goals',
          'Monitor other important event types',
          'Plan for seasonal event variations',
        ],
      );
    }

    return null;
  }

  /// Get general events management best practices
  EventsInsight _getEventsBestPractices() {
    return EventsInsight(
      title: 'Events Management Best Practices',
      description: 'Follow these best practices to optimize your farm event management and improve efficiency.',
      icon: Icons.star,
      color: Colors.green,
      priority: InsightPriority.medium,
      recommendations: [
        'Schedule events in advance with proper planning',
        'Set up reminders for important deadlines',
        'Prioritize events based on urgency and importance',
        'Keep detailed records of all farm activities',
        'Review and update event schedules regularly',
        'Prepare resources and materials ahead of time',
        'Monitor weather and seasonal factors',
        'Maintain consistent event tracking practices',
      ],
    );
  }
}

/// Data classes for events insights
class EventsInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  EventsInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
