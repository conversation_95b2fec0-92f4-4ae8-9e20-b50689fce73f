import 'package:flutter/material.dart';
import '../models/event_isar.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../utils/message_utils.dart';

// --- Constants ---
class _AppStrings {
  static const String addEventTitle = 'Add New Event';
  static const String editEventTitle = 'Edit Event';
  static const String titleLabel = 'Title';
  static const String eventTypeLabel = 'Event Type';
  static const String eventDateLabel = 'Event Date';
  static const String dueDateLabel = 'Due Date (Optional)';
  static const String notesLabel = 'Notes (Optional)';

  // Validation messages

  // Success messages
  static const String addSuccess = 'Event added successfully';
  static const String updateSuccess = 'Event updated successfully';
  static const String saveError = 'Error saving event';
}

class EventFormDialog extends StatefulWidget {
  final String cattleId;
  final EventIsar? event;
  final Function(EventIsar) onSave;

  const EventFormDialog({
    Key? key,
    required this.cattleId,
    required this.onSave,
    this.event,
  }) : super(key: key);

  @override
  State<EventFormDialog> createState() => _EventFormDialogState();
}

class _EventFormDialogState extends State<EventFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _notesController;
  late DateTime _selectedDate;
  DateTime? _selectedDueDate;
  late EventType _selectedType;
  late EventPriority _selectedPriority;
  late String _completionStatus;
  TimeOfDay? _selectedTime;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _notesController = TextEditingController();
    _completionStatus = 'Pending'; // Set default status
    _initializeFromEvent();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeFromEvent() {
    if (widget.event != null) {
      EventIsar event = widget.event!;
      _titleController.text = event.title ?? '';
      _selectedDate = event.eventDate ?? DateTime.now();
      _selectedDueDate = event.dueDate;
      _selectedType = event.type?.toEventType() ?? EventType.miscellaneous;
      _selectedPriority = event.priority;
      _completionStatus = event.isCompleted
          ? 'Completed'
          : (event.isMissed ? 'Missed' : 'Pending');
      _notesController.text = event.notes ?? '';

      // Safely handle time property
      if (event.time != null) {
        _selectedTime = event.time?.toTimeOfDay();
      } else {
        _selectedTime = null;
      }
    } else {
      // Set default values for new event
      _selectedDate = DateTime.now();
      _selectedType = EventType.miscellaneous;
      _selectedPriority = EventPriority.medium;
      _selectedTime = null;
    }
  }

  Future<EventIsar> _buildEventFromForm() async {
    final event = widget.event ?? EventIsar();

    event.title = _titleController.text.trim();
    event.eventDate = _selectedDate;
    event.dueDate = _selectedDueDate;

    // Setup event type
    event.type ??= EventTypeEmbedded();
    event.type!.fromEventType(_selectedType);

    event.priority = _selectedPriority;
    event.notes = _notesController.text.trim();

    // Set completion status
    event.isCompleted = _completionStatus == 'Completed';
    event.isMissed = _completionStatus == 'Missed';

    // Set time if selected
    if (_selectedTime != null) {
      event.time = TimeOfDayIsar.fromTimeOfDay(_selectedTime!);
    } else {
      event.time = null;
    }

    event.updatedAt = DateTime.now();

    return event;
  }



  @override
  Widget build(BuildContext context) {
    return widget.event == null
        ? UniversalFormDialog(
            title: _AppStrings.addEventTitle,
            headerIcon: Icons.event, // Event-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editEventTitle,
            headerIcon: Icons.event, // Event-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title Field
          UniversalFormField.textField(
            label: _AppStrings.titleLabel,
            controller: _titleController,
            prefixIcon: Icons.title,
            prefixIconColor: Colors.blue,
            validator: (value) => UniversalFormField.requiredValidator(value, 'title'),
          ),
          UniversalFormField.spacing,

          // Event Type Dropdown
          UniversalFormField.dropdownField<EventType>(
            label: _AppStrings.eventTypeLabel,
            value: _selectedType,
            items: EventType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type.name),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedType = value;
                });
              }
            },
            prefixIcon: Icons.priority_high,
            prefixIconColor: Colors.orange,
            validator: (value) => UniversalFormField.dropdownValidator(value, 'priority'),
          ),
          UniversalFormField.spacing,

          // Event Date Field
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.eventDateLabel,
            value: _selectedDate,
            onChanged: (date) {
              setState(() {
                _selectedDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
          ),
          UniversalFormField.spacing,

          // Due Date Field (Optional)
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.dueDateLabel,
            value: _selectedDueDate,
            onChanged: (date) {
              setState(() {
                _selectedDueDate = date;
              });
            },
            prefixIcon: Icons.schedule,
            prefixIconColor: Colors.green,
          ),
          UniversalFormField.spacing,

          // Notes Field
          UniversalFormField.multilineField(
            label: _AppStrings.notesLabel,
            controller: _notesController,
            maxLines: 3,
            prefixIcon: Icons.notes,
            prefixIconColor: Colors.teal,
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final event = await _buildEventFromForm();

      widget.onSave(event);

      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.event != null
              ? _AppStrings.updateSuccess
              : _AppStrings.addSuccess,
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, '${_AppStrings.saveError}: $e');
      }
    }
  }
}
