import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../controllers/breeding_details_controller.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_bar.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';

import '../dialogs/breeding_form_dialog.dart';
import '../dialogs/pregnancy_form_dialog.dart';
import '../dialogs/delivery_form_dialog.dart';
import 'breeding_details_analytics_tab.dart';
import 'breeding_details_breeding_tab.dart';
import 'breeding_details_pregnancy_tab.dart';
import 'breeding_details_delivery_tab.dart';

class BreedingDetailsScreen extends StatefulWidget {
  final CattleIsar cattle;
  final String businessId;
  final Function(CattleIsar)? onCattleUpdated;

  const BreedingDetailsScreen({
    Key? key,
    required this.cattle,
    required this.businessId,
    this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<BreedingDetailsScreen> createState() => _BreedingDetailsScreenState();
}

class _BreedingDetailsScreenState extends State<BreedingDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      debugPrint('DEBUG: BreedingDetailsScreen.initState called');
      debugPrint('DEBUG: businessId = ${widget.businessId}');
      debugPrint('DEBUG: cattle.name = ${widget.cattle.name}');
      debugPrint('DEBUG: cattle.businessId = ${widget.cattle.businessId}');
    }

    // Analytics, Breeding, Pregnancy, and Delivery tabs
    _tabController = TabController(
      length: 4, // Analytics, Breeding, Pregnancy, Delivery
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddBreedingDialog(BreedingDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => BreedingFormDialog(
        initialCattleId: controller.cattle!.tagId, // Use tagId instead of businessId
        onSave: (record) async {
          final success = await controller.addBreedingRecord(record);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Breeding record added successfully');
          }
        },
      ),
    );
  }

  void _showAddPregnancyDialog(BreedingDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => PregnancyFormDialog(
        initialCattleId: controller.cattle!.tagId, // Use tagId instead of businessId
        onSave: (record) async {
          final success = await controller.addPregnancyRecord(record);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Pregnancy record added successfully');
          }
        },
      ),
    );
  }

  void _showAddDeliveryDialog(BreedingDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => DeliveryFormDialog(
        existingCattle: [controller.cattle!],
        motherTagId: controller.cattle!.tagId,
        onSave: (record) async {
          final success = await controller.addDeliveryRecord(record);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Delivery record added successfully');
          }
        },
      ),
    );
  }

  void _getCurrentTabAction(BreedingDetailsController controller) {
    switch (_tabController.index) {
      case 0:
        // Analytics tab - no FAB action
        break;
      case 1:
        _showAddBreedingDialog(controller);
        break;
      case 2:
        _showAddPregnancyDialog(controller);
        break;
      case 3:
        _showAddDeliveryDialog(controller);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<BreedingDetailsController>(
      create: (_) => BreedingDetailsController()..initialize(widget.cattle), // Fixed: Create directly, not from GetIt
      child: Consumer<BreedingDetailsController>(
        builder: (context, controller, child) {
          // Handle different states
          if (controller.isLoading) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: 'Loading...',
              ),
              body: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (controller.error != null) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: 'Error',
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load breeding details',
                      style: Theme.of(context).textTheme.headlineSmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      controller.error ?? 'Unknown error occurred',
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () => controller.refresh(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return Builder(
            builder: (context) {
              // Initialize tab manager here where Provider context is available
              _tabManager ??= UniversalTabManager.fourTabs(
                controller: _tabController,
                tabViews: const [
                  BreedingDetailsAnalyticsTab(),
                  BreedingDetailsBreedingTab(),
                  BreedingDetailsPregnancyTab(),
                  BreedingDetailsDeliveryTab(),
                ],
                labels: const ['Analytics', 'Breeding', 'Pregnancy', 'Delivery'],
                icons: const [Icons.analytics, Icons.favorite, Icons.pregnant_woman, Icons.child_care],
                showFABs: const [false, true, true, true], // No FAB on analytics, FABs on record tabs
              );

              return Scaffold(
                appBar: AppBarConfig.withBack(
                  context: context,
                  title: '${controller.cattle?.name ?? 'Breeding'} (${controller.cattle?.tagId ?? 'No Tag'})',
                ),
                body: _tabManager!,
                floatingActionButton: AnimatedBuilder(
                  animation: _tabController,
                  builder: (context, child) {
                    // Only rebuild FAB when tab changes, not the entire screen
                    return _tabManager?.getCurrentFAB(
                      onPressed: () => _getCurrentTabAction(controller), // Pass controller
                      tooltip: 'Add Record',
                      backgroundColor: AppColors.breedingHeader,
                    ) ?? const SizedBox.shrink(); // Handle null case
                  },
                ), // Optimized FAB management with AnimatedBuilder
              );
            },
          );
        },
      ),
    );
  }
}