// LEGACY FILE - COMMENTED OUT FOR ANALYSIS PURPOSES
// This file contains legacy pregnancy implementation for reference
// DO NOT UNCOMMENT - Use for feature analysis only

/*
import 'package:flutter/material.dart';
import 'dart:async'; // Add this for StreamSubscription
import 'package:flutter/foundation.dart'; // Add this for mapEquals
import 'package:intl/intl.dart';
import '../../models/cattle_isar.dart'; // Import CattleIsar

// Add this for AnimalType
import 'package:cattle_manager/services/database/database_helper.dart';
import '../../../Breeding/dialogs/pregnancy_form_dialog.dart';
import '../../../Breeding/dialogs/delivery_form_dialog.dart'; // Updated import path
import '../../widgets/index.dart'; // Import reusable widgets
import '../../../Breeding/models/breeding_record_isar.dart'; // Import BreedingRecordIsar
import '../../../Breeding/models/pregnancy_record_isar.dart';
// Import our new widget

class PregnancyView extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar) onCattleUpdated;
  final DatabaseHelper databaseHelper = DatabaseHelper.instance;

  PregnancyView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
  });

  @override
  State<PregnancyView> createState() => _PregnancyViewState();
}

class _PregnancyViewState extends State<PregnancyView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  bool _isLoading = false;
  bool _isPregnant = false;
  DateTime? _dueDate;
  String? _pregnancyStatus;
  int _pregnancyDays = 0;
  double _progressPercentage = 0.0;
  List<Map<String, dynamic>> _milestones = [];
  BreedingRecordIsar? _currentBreedingRecord;
  List<CattleIsar> _allCattle = [];
  StreamSubscription? _breedingRecordSubscription;
  bool _isProcessingState = false;
  Timer? _stateDebounceTimer;
  Map<String, dynamic>? _activePregnancyRecord;
  List<Map<String, dynamic>> _pregnancyRecords = [];
  // Add this field at the class level near other field declarations
  final GlobalKey _pregnancyViewKey = GlobalKey();
  // Add this field at the class level near other field declarations

  // Add stable keys for widgets

  // Add minimum heights for cards to prevent jumping

  // Method to ensure milestones are generated if the cattle is pregnant
  void _ensureMilestonesGenerated() {
    debugPrint(
        'Ensuring milestones are generated - isPregnant: $_isPregnant, milestones count: ${_milestones.length}');

    if (_isPregnant && _milestones.isEmpty) {
      debugPrint('Need to regenerate milestones');

      // Get the animal type for this cattle to get the correct gestation period
      _databaseHelper.farmSetupHandler.getAllAnimalTypes().then((animalTypes) {
        final animalType = animalTypes.firstWhere(
          (type) =>
              type.id.toString() == widget.cattle.animalTypeId?.toString(),
          orElse: () => animalTypes.first,
        );

        final gestationDays = animalType.defaultGestationDays ?? 280;

        // Try to use the current breeding record if available
        if (_currentBreedingRecord != null) {
          try {
            debugPrint('Regenerating milestones from current breeding record');
            final breedingDate = _currentBreedingRecord!.date;
            _generateMilestones(breedingDate, gestationDays);
          } catch (e) {
            debugPrint('Error generating milestones from breeding record: $e');
            _generateMilestonesFromPregnancyData(gestationDays);
          }
        } else {
          // Fallback to using pregnancy data
          _generateMilestonesFromPregnancyData(gestationDays);
        }

        // Update the UI
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _loadAllCattle();
    _loadPregnancyRecords();
    _checkPregnancyStatus();
    _subscribeToBreedingRecordUpdates();

    // Add a post-frame callback to ensure milestones are generated after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureMilestonesGenerated();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Initial load when the view is first created
    _checkPregnancyStatus().then((_) {
      // Ensure milestones are generated after checking pregnancy status
      _ensureMilestonesGenerated();
    });
  }

  @override
  void dispose() {
    _breedingRecordSubscription?.cancel();
    _stateDebounceTimer?.cancel();
    super.dispose();
  }

  // Subscribe to breeding record updates
  void _subscribeToBreedingRecordUpdates() {
    Timer? debounceTimer;
    Map<String, dynamic>? lastUpdate;

    _breedingRecordSubscription =
        _databaseHelper.breedingRecordStream.listen((event) async {
      // Get the event details
      final eventCattleId = event['cattleId'];
      final action = event['action'];

      // Check if this event is relevant to our cattle
      if (eventCattleId == widget.cattle.tagId) {
        debugPrint(
            'Breeding record update received: $action for cattle $eventCattleId');

        // Cancel any pending debounce timer
        debounceTimer?.cancel();
        _stateDebounceTimer?.cancel();

        // Set a new debounce timer with longer duration
        debounceTimer = Timer(const Duration(milliseconds: 5000), () async {
          if (!mounted || _isProcessingState) return;

          try {
            _isProcessingState = true;

            // Always fetch fresh cattle data for any breeding-related event
            final updatedCattle = await _databaseHelper.cattleHandler
                .getCattleByTagId(widget.cattle.tagId ?? '');
            if (!mounted) return;

            if (updatedCattle != null) {
              // Get the latest breeding records
              final breedingRecords = await _databaseHelper.breedingHandler
                  .getBreedingRecordsForCattle(widget.cattle.tagId ?? '');
              if (!mounted) return;

              // Sort by date, most recent first
              breedingRecords.sort((a, b) {
                final dateA = tryParseDate(a['date']?.toString());
                final dateB = tryParseDate(b['date']?.toString());

                if (dateA == null && dateB == null) return 0;
                if (dateA == null) return 1;
                if (dateB == null) return -1;

                return dateB.compareTo(dateA);
              });

              // Check for successful breeding records
              if (breedingRecords.isNotEmpty) {
                final latestRecord = breedingRecords.first;
                final latestStatus =
                    latestRecord['status']?.toString().toLowerCase() ?? '';

                // Create a map of the current state for comparison
                final currentState = {
                  'isPregnant': updatedCattle.isPregnant,
                  'lastBreedingDate':
                      updatedCattle.lastBreedingDate?.toIso8601String(),
                  'expectedCalvingDate':
                      updatedCattle.expectedCalvingDate?.toIso8601String(),
                  'latestBreedingStatus': latestStatus,
                  'latestBreedingId': latestRecord['id'],
                };

                // Skip update if the state hasn't changed
                if (mapEquals(lastUpdate, currentState)) {
                  _isProcessingState = false;
                  return;
                }
                lastUpdate = Map<String, dynamic>.from(currentState);

                // Determine the desired pregnancy state based on the latest breeding record
                final shouldBePregnant = latestStatus == 'completed';

                // Only update if the pregnancy status is different from what it should be
                if (updatedCattle.isPregnant != shouldBePregnant) {
                  // Set a timer to prevent rapid state changes
                  _stateDebounceTimer =
                      Timer(const Duration(milliseconds: 2000), () async {
                    if (!mounted) return;

                    try {
                      if (shouldBePregnant) {
                        // Get animal type for this cattle to get the correct gestation period
                        final animalTypes = await _databaseHelper
                            .farmSetupHandler
                            .getAllAnimalTypes();
                        if (!mounted) return;

                        final animalType = animalTypes.firstWhere(
                          (type) =>
                              type.id.toString() ==
                              widget.cattle.animalTypeId?.toString(),
                          orElse: () => animalTypes.first,
                        );
                        final gestationDays =
                            animalType.defaultGestationDays ?? 280;

                        // Calculate expected calving date
                        final breedingDate =
                            tryParseDate(latestRecord['date']?.toString());
                        final expectedCalvingDate = _dueDate ??
                            breedingDate?.add(Duration(days: gestationDays)) ??
                            DateTime.now();

                        final updatedCattleWithPregnancy =
                            updatedCattle.copyWith(
                                breedingStatus: BreedingStatus()
                                  ..isPregnant = true
                                  ..status = 'Pregnant'
                                  ..expectedCalvingDate = expectedCalvingDate);

                        await _databaseHelper.cattleHandler
                            .updateCattle(updatedCattleWithPregnancy);
                        if (!mounted) return;
                        widget.onCattleUpdated(updatedCattleWithPregnancy);
                      } else {
                        final updatedCattleNotPregnant = updatedCattle.copyWith(
                            breedingStatus: BreedingStatus()
                              ..isPregnant = false
                              ..expectedCalvingDate = null);

                        await _databaseHelper.cattleHandler
                            .updateCattle(updatedCattleNotPregnant);
                        if (!mounted) return;
                        widget.onCattleUpdated(updatedCattleNotPregnant);
                      }

                      // Only refresh the view if we're mounted and there were actual changes
                      if (mounted) {
                        await _checkPregnancyStatus();
                      }
                    } finally {
                      if (mounted) {
                        setState(() {
                          _isProcessingState = false;
                        });
                      }
                    }
                  });
                }
              }
            }
          } catch (e) {
            debugPrint('Error updating pregnancy view: $e');
          } finally {
            _isProcessingState = false;
          }
        });
      }
    }, onError: (error) {
      debugPrint('Error in breeding record stream: $error');
    });
  }

  @override
  void didUpdateWidget(PregnancyView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if we're switching to a different cattle or if important properties changed
    if (oldWidget.cattle.id != widget.cattle.id ||
        oldWidget.cattle.isPregnant != widget.cattle.isPregnant ||
        oldWidget.cattle.expectedCalvingDate !=
            widget.cattle.expectedCalvingDate ||
        oldWidget.cattle.lastBreedingDate != widget.cattle.lastBreedingDate) {
      _checkPregnancyStatus();
    }
  }

  Future<void> _checkPregnancyStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint(
          'Checking pregnancy status for cattle: ${widget.cattle.tagId}');

      // Get the latest cattle data from the database to ensure we have the most up-to-date status
      final latestCattle = await _databaseHelper.cattleHandler
          .getCattleByTagId(widget.cattle.tagId ?? '');
      final cattle = latestCattle ?? widget.cattle;

      debugPrint('Cattle pregnancy status: ${cattle.isPregnant}');
      debugPrint('Cattle lastBreedingDate: ${cattle.lastBreedingDate}');
      debugPrint('Cattle expectedCalvingDate: ${cattle.expectedCalvingDate}');

      // Get animal type for this cattle
      final animalTypes =
          await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      final animalType = animalTypes.firstWhere(
        (type) => type.id.toString() == widget.cattle.animalTypeId?.toString(),
        orElse: () => animalTypes.first,
      );

      // Get gestation period for this animal type to use when generating milestones
      final gestationPeriod = animalType.defaultGestationDays ??
          283; // Default to cattle gestation if not specified

      debugPrint(
          'Animal type: ${animalType.name}, gestation days: ${animalType.defaultGestationDays}');

      // Reset pregnancy data
      _isPregnant = false;
      _dueDate = null;
      _pregnancyStatus = null;
      _pregnancyDays = 0;
      _progressPercentage = 0.0;
      _milestones = [];
      _currentBreedingRecord = null;
      _activePregnancyRecord = null;

      // Get pregnancy records for this cattle - force refresh from storage
      _pregnancyRecords = [];
      final pregnancyRecords = await _databaseHelper.breedingHandler
          .getPregnancyRecordsForCattle(cattle.tagId ?? '');
      final pregnancyRecordsAsMaps =
          pregnancyRecords.map((record) => record.toMap()).toList();

      // Fix for duplicate pregnancy records - filter out duplicates by breedingRecordId
      final uniqueRecords = <String, Map<String, dynamic>>{};
      for (final record in pregnancyRecordsAsMaps) {
        final breedingId = record['breedingRecordId'] ?? '';
        if (!uniqueRecords.containsKey(breedingId) ||
            (record['status'] == 'Confirmed' &&
                uniqueRecords[breedingId]?['status'] != 'Confirmed')) {
          uniqueRecords[breedingId] = record;
        }
      }

      _pregnancyRecords = uniqueRecords.values.toList();

      // Sort by date, most recent first
      _pregnancyRecords.sort((a, b) => compareDates(a, b, 'startDate'));

      // Find the active pregnancy record (confirmed status)
      final activePregnancy = _pregnancyRecords
          .where((record) => (record['status'] == 'Confirmed'))
          .toList()
          .lastOrNull;

      // Also try to find a completed pregnancy if no active one exists
      final completedPregnancy = activePregnancy == null
          ? _pregnancyRecords
              .where((record) => record['status'] == 'Completed')
              .toList()
              .lastOrNull
          : null;

      final effectivePregnancy = activePregnancy ?? completedPregnancy;
      _activePregnancyRecord = activePregnancy;

      debugPrint('Active pregnancy found: ${activePregnancy != null}');
      if (activePregnancy != null) {
        debugPrint(
            'Active pregnancy start date: ${activePregnancy['startDate']}');
        debugPrint(
            'Active pregnancy expected calving date: ${activePregnancy['expectedCalvingDate']}');
      }
      debugPrint('Completed pregnancy found: ${completedPregnancy != null}');

      // Check if there's any pregnancy record to display
      if (effectivePregnancy != null) {
        _isPregnant = true;
        final startDate =
            tryParseDate(effectivePregnancy['startDate']?.toString());

        if (startDate != null) {
          if (effectivePregnancy['expectedCalvingDate'] != null) {
            _dueDate = tryParseDate(
                    effectivePregnancy['expectedCalvingDate']?.toString()) ??
                startDate.add(Duration(days: gestationPeriod));
          } else {
            _dueDate = startDate.add(Duration(days: gestationPeriod));
          }

          debugPrint('Pregnancy start date: $startDate, due date: $_dueDate');

          // Get the associated breeding record - force a fresh fetch
          final breedingRecords = await _databaseHelper.breedingHandler
              .getBreedingRecordsForCattle(cattle.tagId ?? '');
          final breedingRecord = breedingRecords
              .where((record) =>
                  record['id'].toString() ==
                  effectivePregnancy['breedingRecordId'].toString())
              .firstOrNull;

          debugPrint(
              'Associated breeding record found: ${breedingRecord != null}');
          if (breedingRecord != null) {
            debugPrint('Breeding record date: ${breedingRecord['date']}');
            debugPrint('Breeding record status: ${breedingRecord['status']}');
          }

          if (breedingRecord != null) {
            try {
              // Fix for breeding record error - validate required fields before creating BreedingRecord
              if (breedingRecord['date'] != null &&
                  breedingRecord['type'] != null &&
                  breedingRecord['status'] != null) {
                _currentBreedingRecord = BreedingRecordIsar.fromMap(
                    breedingRecord as Map<String, dynamic>);
                final breedingDate =
                    tryParseDate(breedingRecord['date']?.toString());

                if (breedingDate != null) {
                  _pregnancyDays =
                      DateTime.now().difference(breedingDate).inDays;

                  debugPrint(
                      'Breeding date: $breedingDate, pregnancy days: $_pregnancyDays');

                  // Calculate progress percentage using animal-specific gestation period
                  final totalGestationDays = gestationPeriod;
                  _progressPercentage = _pregnancyDays / totalGestationDays;
                  if (_progressPercentage > 1.0) _progressPercentage = 1.0;

                  // Set pregnancy status based on progress
                  if (_pregnancyDays < 90) {
                    _pregnancyStatus = 'Early Pregnancy';
                  } else if (_pregnancyDays < 180) {
                    _pregnancyStatus = 'Mid Pregnancy';
                  } else {
                    _pregnancyStatus = 'Late Pregnancy';
                  }

                  debugPrint('Pregnancy status: $_pregnancyStatus');

                  // Generate milestones using the animal-specific gestation period
                  _generateMilestones(breedingDate, totalGestationDays);

                  debugPrint(
                      'After generating milestones, count: ${_milestones.length}');
                } else {
                  debugPrint('Error: Invalid breeding date format');
                  // Set fallback values based on pregnancy data
                  _pregnancyDays = DateTime.now().difference(startDate).inDays;
                  final totalGestationDays = gestationPeriod;
                  _progressPercentage = _pregnancyDays / totalGestationDays;
                  if (_progressPercentage > 1.0) _progressPercentage = 1.0;

                  // Set pregnancy status based on progress
                  if (_pregnancyDays < 90) {
                    _pregnancyStatus = 'Early Pregnancy';
                  } else if (_pregnancyDays < 180) {
                    _pregnancyStatus = 'Mid Pregnancy';
                  } else {
                    _pregnancyStatus = 'Late Pregnancy';
                  }

                  // Try to generate milestones from pregnancy data
                  _generateMilestonesFromPregnancyData(totalGestationDays);
                }
              }
            } catch (e) {
              debugPrint('Error processing breeding record: $e');

              // Set fallback values based on pregnancy data
              _pregnancyDays = DateTime.now().difference(startDate).inDays;
              final totalGestationDays = gestationPeriod;
              _progressPercentage = _pregnancyDays / totalGestationDays;
              if (_progressPercentage > 1.0) _progressPercentage = 1.0;

              // Set pregnancy status based on progress
              if (_pregnancyDays < 90) {
                _pregnancyStatus = 'Early Pregnancy';
              } else if (_pregnancyDays < 180) {
                _pregnancyStatus = 'Mid Pregnancy';
              } else {
                _pregnancyStatus = 'Late Pregnancy';
              }

              // Try to generate milestones from pregnancy data
              _generateMilestonesFromPregnancyData(totalGestationDays);
            }

            // Update cattle if needed
            if (cattle.isPregnant != true ||
                cattle.expectedCalvingDate != _dueDate) {
              final updatedCattle = cattle.copyWith(
                breedingStatus: BreedingStatus()
                  ..isPregnant = true
                  ..status = 'Pregnant'
                  ..expectedCalvingDate = _dueDate,
              );

              await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
              widget.onCattleUpdated(updatedCattle);
            }
          } else {
            // Try to generate milestones from pregnancy data
            _generateMilestonesFromPregnancyData(gestationPeriod);
          }
        }
      }

      debugPrint(
          'Final pregnancy status: $_isPregnant, milestones count: ${_milestones.length}');

      // Ensure milestones are generated after checking pregnancy status
      _ensureMilestonesGenerated();
    } catch (e) {
      debugPrint('Error checking pregnancy status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error checking pregnancy status: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        debugPrint(
            'Final pregnancy status: $_isPregnant, milestones count: ${_milestones.length}');

        // Ensure milestones are generated after checking pregnancy status
        _ensureMilestonesGenerated();
      }
    }
  }

  void _generateMilestones(DateTime? breedingDate, int gestationDays) {
    _milestones = [];

    if (breedingDate == null) {
      debugPrint('Cannot generate milestones: breeding date is null');
      return;
    }

    debugPrint(
        'Generating milestones for breeding date: ${breedingDate.toString()}, gestation days: $gestationDays');

    final now = DateTime.now();

    // Pregnancy confirmation milestone (3 months)
    final confirmationDate = breedingDate.add(const Duration(days: 90));
    _milestones.add({
      'title': 'Pregnancy Confirmation',
      'description': 'Confirm pregnancy via veterinary check',
      'date': confirmationDate,
      'icon': Icons.check_circle,
      'color': Colors.blue,
      'isPassed': now.isAfter(confirmationDate),
    });

    // Mid-pregnancy check milestone (5 months)
    final midCheckDate = breedingDate.add(const Duration(days: 150));
    _milestones.add({
      'title': 'Mid-Pregnancy Check',
      'description': 'Perform mid-pregnancy health assessment',
      'date': midCheckDate,
      'icon': Icons.medical_services,
      'color': Colors.purple,
      'isPassed': now.isAfter(midCheckDate),
    });

    // Dry-off period milestone (60 days before due date)
    final dryOffDate = breedingDate.add(Duration(days: gestationDays - 60));
    _milestones.add({
      'title': 'Dry-Off Period',
      'description': 'Stop milking 60 days before expected calving',
      'date': dryOffDate,
      'icon': Icons.no_drinks,
      'color': Colors.orange,
      'isPassed': now.isAfter(dryOffDate),
    });

    // Due date approaching milestone (7 days before due date)
    final approachingDate = breedingDate.add(Duration(days: gestationDays - 7));
    _milestones.add({
      'title': 'Due Date Approaching',
      'description': 'Prepare for calving within a week',
      'date': approachingDate,
      'icon': Icons.access_alarm,
      'color': Colors.red,
      'isPassed': now.isAfter(approachingDate),
    });

    // Due date milestone
    final dueDate = breedingDate.add(Duration(days: gestationDays));
    _milestones.add({
      'title': 'Expected Due Date',
      'description': 'Expected calving date',
      'date': dueDate,
      'icon': Icons.child_care,
      'color': Colors.green,
      'isPassed': now.isAfter(dueDate),
    });

    // Sort milestones by date
    _milestones.sort(
        (a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));

    debugPrint('Generated ${_milestones.length} milestones');
  }

  // Method to check if close to delivery date
  bool _isCloseToDeliveryDate() {
    if (_dueDate == null) return false;

    final now = DateTime.now();
    final daysUntilDue = _dueDate!.difference(now).inDays;

    // Allow recording birth within 30 days before due date or any time after
    return daysUntilDue <= 30;
  }

  // Method to record birth
  Future<void> _recordBirth() async {
    // Show the delivery form dialog
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => DeliveryFormDialog(
        motherTagId: widget.cattle.tagId,
        existingCattle: _allCattle,
      ),
    );

    if (result != null && mounted) {
      try {
        setState(() => _isLoading = true);

        // Add the delivery record to the database
        if (result.containsKey('motherTagId') &&
            result['motherTagId'] == widget.cattle.tagId) {
          // Format the delivery record properly
          final deliveryRecord = {
            'motherTagId': result['motherTagId'],
            'date': result['deliveryDate'],
            'type': result['deliveryType'],
            'numberOfCalves': result['numberOfCalves'],
            'notes': result['notes'],
            'calfDetails': result['calfDetails'],
            'status': 'Completed',
          };

          // Add the delivery record
          await _databaseHelper.breedingHandler
              .addDeliveryRecordFromMap(deliveryRecord);

          // Create new calves if present in the result
          if (result.containsKey('newCalves') && result['newCalves'] is List) {
            for (final calfData in result['newCalves']) {
              if (calfData is Map<String, dynamic>) {
                await _databaseHelper.cattleHandler
                    .addCattle(CattleIsar.fromMap(calfData));
              }
            }
          }
        }

        // Update the pregnancy record status to Completed
        if (_activePregnancyRecord != null) {
          final updatedPregnancyRecord =
              Map<String, dynamic>.from(_activePregnancyRecord!);
          updatedPregnancyRecord['status'] = 'Completed';
          updatedPregnancyRecord['completionDate'] =
              DateTime.now().toIso8601String();

          final pregnancyRecord =
              PregnancyRecordIsar.fromMap(updatedPregnancyRecord);
          await _databaseHelper.breedingHandler
              .addOrUpdatePregnancyRecord(pregnancyRecord);

          // Also update the associated breeding record if it exists
          final breedingRecords = await _databaseHelper.breedingHandler
              .getBreedingRecordsForCattle(widget.cattle.tagId ?? '');
          final matchingRecord = breedingRecords.firstWhere(
            (br) =>
                br.businessId?.toString() ==
                updatedPregnancyRecord['breedingRecordId']?.toString(),
            orElse: () => BreedingRecordIsar(),
          );

          if (matchingRecord.businessId != null) {
            // Update the breeding record status to Completed
            final updatedBreedingRecord = matchingRecord.toMap();
            updatedBreedingRecord['status'] = 'Completed';
            updatedBreedingRecord['notes'] =
                'Breeding completed with successful delivery on ${DateFormat('MMM dd, yyyy').format(DateTime.now())}';

            // Save the updated breeding record
            await _databaseHelper.breedingHandler
                .updateBreedingRecordFromMap(updatedBreedingRecord);

            // Notify record update with the map version
            _databaseHelper.notifyRecordUpdate('breeding', 'update',
                widget.cattle.tagId ?? '', updatedBreedingRecord);
          }
        }

        // Update the cattle's status
        final updatedCattle = widget.cattle.copyWith(
          breedingStatus: BreedingStatus()
            ..isPregnant = false
            ..status = 'Open'
            ..expectedCalvingDate = null,
        );

        await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

        // Notify parent of update
        widget.onCattleUpdated(updatedCattle);

        // Refresh the records
        await _loadPregnancyRecords();
        await _checkPregnancyStatus();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Birth recorded successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error recording birth: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  Widget _buildPregnancyHistoryCard({Key? key}) {
    // Use a more stable key string that doesn't include milliseconds
    final stableKeyString =
        'history_${widget.cattle.isPregnant}_${widget.cattle.lastBreedingDate?.toIso8601String()}_${widget.cattle.expectedCalvingDate?.toIso8601String()}';

    return FutureBuilder<List<Map<String, dynamic>>>(
      key: ValueKey(stableKeyString),
      future: _databaseHelper.breedingHandler
          .getPregnancyRecordsForCattle(widget.cattle.tagId ?? '')
          .then((records) => records.map((record) => record.toMap()).toList()),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        } else if (snapshot.hasError) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Error loading pregnancy records: ${snapshot.error}',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          );
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'No pregnancy records found for this animal.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          );
        } else {
          final records = snapshot.data!;
          // Sort by date, most recent first
          records.sort((a, b) {
            final dateA = tryParseDate(a['startDate']?.toString());
            final dateB = tryParseDate(b['startDate']?.toString());

            if (dateA == null && dateB == null) return 0;
            if (dateA == null) return 1;
            if (dateB == null) return -1;

            return dateB.compareTo(dateA);
          });

          // Add cattleName and cattleId to each record for display
          for (final record in records) {
            record['cattleName'] = widget.cattle.name ?? '';
            record['cattleId'] = widget.cattle.tagId ?? '';
          }

          return PregnancyHistoryCard(
            key: key,
            records: records,
            title: 'Pregnancy History',
            emptyMessage: 'No pregnancy history available',
            onEdit: _editPregnancyRecord,
            onDelete: _deletePregnancyRecord,
            onStatusTap: _editPregnancyRecord,
          );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Debug print to check milestones at build time
    debugPrint(
        'Building UI - isPregnant: $_isPregnant, milestones count: ${_milestones.length}');

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _checkPregnancyStatus,
        child: SingleChildScrollView(
          key: _pregnancyViewKey,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistics Card
              (() {
                // Add a unique key based on cattle state to force rebuild when state changes
                final keyString =
                    'stats_${widget.cattle.isPregnant}_${widget.cattle.lastBreedingDate?.toIso8601String()}_${widget.cattle.expectedCalvingDate?.toIso8601String()}';

                return FutureBuilder<List<Map<String, dynamic>>>(
                  key: ValueKey(keyString),
                  future: _databaseHelper.breedingHandler
                      .getPregnancyRecordsForCattle(widget.cattle.tagId ?? '')
                      .then((records) =>
                          records.map((record) => record.toMap()).toList()),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Card(
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: CircularProgressIndicator(),
                          ),
                        ),
                      );
                    }

                    if (snapshot.hasError) {
                      return Card(
                        child: Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Text(
                              'Error loading pregnancy statistics: ${snapshot.error}',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        ),
                      );
                    }

                    final pregnancyRecords = snapshot.data ?? [];

                    // Calculate statistics from pregnancy records
                    int totalPregnancies = pregnancyRecords.length;
                    int activePregnancies = pregnancyRecords
                        .where((r) =>
                            r['status']?.toString().toLowerCase() ==
                            'confirmed')
                        .length;
                    int completedPregnancies = pregnancyRecords
                        .where((r) =>
                            r['status']?.toString().toLowerCase() ==
                            'completed')
                        .length;
                    int abortions = pregnancyRecords
                        .where((r) =>
                            r['status']?.toString().toLowerCase() == 'abortion')
                        .length;

                    // Use the factory method instead of manually creating StatsCard
                    return StatsCard.pregnancyStats(
                      totalPregnancies: totalPregnancies,
                      completedPregnancies: completedPregnancies,
                      abortions: abortions,
                      confirmedPregnancies: activePregnancies,
                      onInfoTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Shows detailed information about pregnancy statistics'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      },
                      onCardTap: () {
                        // Optional: Navigate to detailed pregnancy records view
                      },
                    );
                  },
                );
              })(),

              const SizedBox(height: 16),

              // Pregnancy Status Card - Show when pregnant
              if (_isPregnant) ...[
                (() {
                  // Debug logs to help diagnose issues
                  debugPrint(
                      '_activePregnancyRecord: ${_activePregnancyRecord != null}');
                  if (_activePregnancyRecord != null) {
                    final status = _activePregnancyRecord!['status']
                            ?.toString()
                            .toLowerCase() ??
                        '';
                    debugPrint('Pregnancy status: $status');
                    debugPrint('Is completed? ${status == 'completed'}');
                  }

                  // Check if the active pregnancy record is in 'Completed' status
                  final isCompleted = _activePregnancyRecord != null &&
                      (_activePregnancyRecord!['status']
                              ?.toString()
                              .toLowerCase() ==
                          'completed');

                  debugPrint('Showing completed pregnancy UI? $isCompleted');

                  // Get start date from active pregnancy record or cattle's lastBreedingDate
                  DateTime? startDate;
                  if (_activePregnancyRecord != null &&
                      _activePregnancyRecord!['startDate'] != null) {
                    startDate = tryParseDate(
                        _activePregnancyRecord!['startDate']?.toString());
                  } else if (widget.cattle.lastBreedingDate != null) {
                    startDate = widget.cattle.lastBreedingDate;
                  }

                  // For completed pregnancies, we use a different approach
                  if (isCompleted) {
                    // Get completion date if available
                    DateTime? completionDate;
                    if (_activePregnancyRecord!['completionDate'] != null) {
                      completionDate = tryParseDate(
                          _activePregnancyRecord!['completionDate']
                              ?.toString());
                    }

                    // Return a card with completion info
                    return Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header with consistent styling
                          const CardHeader(
                            title: 'Completed Pregnancy',
                            color: Color(0xFF9C27B0), // Purple for completed
                            icon: Icons.check_circle,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                InfoRow(
                                  icon: Icons.calendar_today,
                                  label: 'Breeding Date',
                                  value: startDate != null
                                      ? DateFormat('MMMM dd, yyyy')
                                          .format(startDate)
                                      : 'Not recorded',
                                  color: const Color(0xFF1976D2), // Blue
                                ),
                                const SizedBox(height: 12),
                                InfoRow(
                                  icon: Icons.event_available,
                                  label: 'Completion Date',
                                  value: completionDate != null
                                      ? DateFormat('MMMM dd, yyyy')
                                          .format(completionDate)
                                      : 'Not recorded',
                                  color: const Color(0xFF9C27B0), // Purple
                                  isHighlighted: true,
                                ),
                                if (_activePregnancyRecord!['notes']
                                        ?.isNotEmpty ==
                                    true) ...[
                                  const SizedBox(height: 16),
                                  const Divider(),
                                  const SizedBox(height: 8),
                                  InfoRow(
                                    icon: Icons.notes,
                                    label: 'Notes',
                                    value:
                                        _activePregnancyRecord!['notes'] ?? '',
                                    color:
                                        const Color(0xFF673AB7), // Deep Purple
                                    isMultiline: true,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // For ongoing pregnancies, use our StatusCard widget
                  return StatusCard.pregnancy(
                    isPregnant: true,
                    dueDate: widget.cattle.expectedCalvingDate,
                    startDate: startDate,
                    title: 'Current Pregnancy',
                    baseColor:
                        const Color(0xFF2E7D32), // Green for active pregnancy
                    trailing: IconButton(
                      icon: const Icon(Icons.info_outline,
                          color: Color(0xFF2E7D32)),
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Pregnancy Status Information'),
                            content: const Text(
                                'This card shows the current pregnancy status and expected calving date for this cattle.'),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: const Text('Close'),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    actionButtons: _isCloseToDeliveryDate()
                        ? [
                            ElevatedButton.icon(
                              onPressed: _recordBirth,
                              icon: const Icon(Icons.child_care),
                              label: const Text('Record Birth'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF2E7D32),
                                foregroundColor: Colors.white,
                                minimumSize: const Size(double.infinity, 48),
                              ),
                            ),
                          ]
                        : null,
                  );
                })(),
                const SizedBox(height: 16),
              ],

              // Pregnancy Eligibility Card - Only show when not pregnant
              if (!_isPregnant) ...[
                _buildEligibilityCard(),
                const SizedBox(height: 16),
              ],

              // Add PregnancyHistoryCard
              _buildPregnancyHistoryCard(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      // Add FAB if not pregnant
      floatingActionButton: !_isPregnant
          ? FloatingActionButton(
              onPressed: _showPregnancyForm,
              backgroundColor: const Color(0xFF2E7D32),
              tooltip: 'Add Pregnancy Record',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildEligibilityCard() {
    return EligibilityCard.pregnancy(
      gender: widget.cattle.gender ?? '',
      dateOfBirth: widget.cattle.dateOfBirth,
      purchaseDate: widget.cattle.purchaseDate,
      cattleId: widget.cattle.tagId ?? '',
      animalTypeId: widget.cattle.animalTypeId ?? '',
      isPregnant: _isPregnant,
      onAddPressed: _showPregnancyForm,
      trailing: IconButton(
        icon: const Icon(Icons.info_outline, color: Color(0xFF6A1B9A)),
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Pregnancy Eligibility Information'),
              content: const Text(
                  'This card shows the eligibility for pregnancy checks. The system checks for gender, age, and other factors.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Update the _showPregnancyForm method to check eligibility first
  void _showPregnancyForm() async {
    try {
      setState(() => _isLoading = true);

      // Get any existing pregnancy records for this cattle

      // Check if already pregnant using the eligibility card logic
      final eligibilityResult = _checkPregnancyEligibility();

      if (!eligibilityResult.isEligible) {
        // Show a dialog explaining why pregnancy recording is not available
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Row(
                children: [
                  Icon(eligibilityResult.statusIcon,
                      color: eligibilityResult.statusColor),
                  const SizedBox(width: 8),
                  const Text('Not Available'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(eligibilityResult.reasonMessage),
                  if (eligibilityResult.nextEligibleDateMessage != null) ...[
                    const SizedBox(height: 12),
                    Text(eligibilityResult.nextEligibleDateMessage!),
                  ],
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        }
        setState(() => _isLoading = false);
        return;
      }

      // Get the animal type for this cattle to get the correct gestation period
      final animalTypes =
          await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      animalTypes.firstWhere(
        (type) => type.id.toString() == widget.cattle.animalTypeId?.toString(),
        orElse: () => animalTypes.first,
      );

      // Get gestation period for this animal type to use when generating milestones
// Default to cattle gestation if not specified

      if (!mounted) return;

      // Show the pregnancy form dialog
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (dialogContext) => PregnancyFormDialog(
          initialCattleId: widget.cattle.tagId,
          breedingRecordId: null,
        ),
      );

      // If the user submitted the form
      if (result != null && mounted) {
        await _addPregnancyRecord(result);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error showing pregnancy form: $e')),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Update _checkPregnancyEligibility to use the static method from EligibilityCard
  EligibilityCheckResult _checkPregnancyEligibility() {
    return EligibilityCard.checkPregnancyEligibility(
      gender: widget.cattle.gender ?? '',
      cattleId: widget.cattle.tagId ?? '',
      isPregnant: _isPregnant,
      dateOfBirth: widget.cattle.dateOfBirth,
      animalTypeId: widget.cattle.animalTypeId ?? '',
      purchaseDate: widget.cattle.purchaseDate,
    );
  }

  void _generateMilestonesFromPregnancyData(int gestationDays) {
    _milestones = [];

    if (_activePregnancyRecord == null && _dueDate == null) {
      debugPrint(
          'Cannot generate milestones: no active pregnancy record or due date');
      return;
    }

    // Use the pregnancy start date from the active record, or fallback to calculating from due date
    DateTime? startDate;
    if (_activePregnancyRecord != null &&
        _activePregnancyRecord!['startDate'] != null) {
      startDate =
          tryParseDate(_activePregnancyRecord!['startDate']?.toString());
      if (startDate == null) {
        debugPrint(
            'Cannot determine pregnancy start date - invalid date format');
        return;
      }
    } else if (_dueDate != null) {
      // Calculate backwards from due date
      startDate = _dueDate!.subtract(Duration(days: gestationDays));
    }

    if (startDate == null) {
      debugPrint('Cannot determine pregnancy start date - no data available');
      return;
    }

    debugPrint(
        'Generating milestones from pregnancy data with start date: $startDate');

    // Generate milestones using the start date and gestation period
    _generateMilestones(startDate, gestationDays);
  }

  Future<void> _loadAllCattle() async {
    try {
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      if (mounted) {
        setState(() {
          _allCattle = allCattle.cast<CattleIsar>();
        });
      }
    } catch (e) {
      debugPrint('Error loading all cattle: $e');
    }
  }

  Future<void> _loadPregnancyRecords() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get pregnancy records for this cattle
      final pregnancyRecords = await _databaseHelper.breedingHandler
          .getPregnancyRecordsForCattle(widget.cattle.tagId ?? '');
      final pregnancyRecordsAsMaps =
          pregnancyRecords.map((record) => record.toMap()).toList();

      // Sort by date, most recent first
      pregnancyRecordsAsMaps.sort((a, b) => compareDates(a, b, 'startDate'));

      // Find the active pregnancy record (confirmed status)
      final activePregnancy = pregnancyRecordsAsMaps
          .where((record) => (record['status'] == 'Confirmed'))
          .toList()
          .lastOrNull;

      if (mounted) {
        setState(() {
          _pregnancyRecords = pregnancyRecordsAsMaps;
          _activePregnancyRecord = activePregnancy;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading pregnancy records: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _addPregnancyRecord(Map<String, dynamic> result) async {
    try {
      setState(() => _isLoading = true);

      // Add or update the pregnancy record directly
      await _databaseHelper.breedingHandler
          .addOrUpdatePregnancyRecord(PregnancyRecordIsar.fromMap(result));

      // Update the cattle's status
      DateTime? expectedCalvingDate;
      if (result['expectedCalvingDate'] != null) {
        expectedCalvingDate =
            tryParseDate(result['expectedCalvingDate']?.toString());
      }

      final updatedCattle = widget.cattle.copyWith(
        breedingStatus: BreedingStatus()
          ..isPregnant = true
          ..status = 'Pregnant'
          ..expectedCalvingDate = expectedCalvingDate,
      );

      await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

      // Notify parent of update
      widget.onCattleUpdated(updatedCattle);

      // Refresh the pregnancy status and records
      await _loadPregnancyRecords();
      await _checkPregnancyStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Pregnancy record added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding pregnancy record: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _editPregnancyRecord(Map<String, dynamic> record) async {
    try {
      setState(() => _isLoading = true);

      // Update the pregnancy record
      final pregnancyRecord = PregnancyRecordIsar.fromMap(record);
      await _databaseHelper.breedingHandler
          .addOrUpdatePregnancyRecord(pregnancyRecord);

      // Check if this is the active pregnancy record
      final isActiveRecord = _activePregnancyRecord != null &&
          record['id'] != null &&
          record['id'].toString() == _activePregnancyRecord?['id']?.toString();

      if (isActiveRecord) {
        // Update the cattle's status if this is the active record
        DateTime? expectedCalvingDate;
        if (record['expectedCalvingDate'] != null) {
          expectedCalvingDate =
              tryParseDate(record['expectedCalvingDate']?.toString());
        }

        final updatedCattle = widget.cattle.copyWith(
          breedingStatus: BreedingStatus()
            ..isPregnant = record['status'] == 'Confirmed'
            ..status = record['status'] == 'Confirmed' ? 'Pregnant' : 'Open'
            ..expectedCalvingDate = expectedCalvingDate,
        );

        await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

        // Notify parent of update
        widget.onCattleUpdated(updatedCattle);
      }

      // Refresh the pregnancy status and records
      await _loadPregnancyRecords();
      await _checkPregnancyStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Pregnancy record updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating pregnancy record: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _deletePregnancyRecord(Map<String, dynamic> record) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text(
            'Are you sure you want to delete this pregnancy record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true && mounted) {
      try {
        setState(() => _isLoading = true);

        // Delete the pregnancy record
        final pregnancyRecord = PregnancyRecordIsar.fromMap(record);
        await _databaseHelper.breedingHandler
            .deletePregnancyRecord(pregnancyRecord.businessId ?? '');

        final recordMap = pregnancyRecord.toMap();
        _databaseHelper.notifyRecordUpdate(
            'pregnancy', 'delete', widget.cattle.tagId ?? '', recordMap);

        // Check if this was the active pregnancy record
        final wasActiveRecord = _activePregnancyRecord != null &&
            record['id'] != null &&
            record['id'].toString() ==
                _activePregnancyRecord?['id']?.toString();

        if (wasActiveRecord) {
          // Update the cattle's status
          final updatedCattle = widget.cattle.copyWith(
            breedingStatus: BreedingStatus()
              ..isPregnant = false
              ..status = 'Open'
              ..expectedCalvingDate = null,
          );

          await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

          // Notify parent of update
          widget.onCattleUpdated(updatedCattle);
        }

        // Refresh the pregnancy status and records
        await _loadPregnancyRecords();
        await _checkPregnancyStatus();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Pregnancy record deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting pregnancy record: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }
}

DateTime? calculateExpectedCalvingDate(
    DateTime? breedingDate, int gestationDays) {
  if (breedingDate == null) return null;
  return breedingDate.add(Duration(days: gestationDays));
}

// Helper method to safely parse dates
DateTime? tryParseDate(String? dateStr) {
  if (dateStr == null) return null;
  try {
    return DateTime.parse(dateStr);
  } catch (e) {
    debugPrint('Error parsing date: $e');
    return null;
  }
}

// Helper method to safely parse dates with a default value
DateTime parseDateWithDefault(String? dateStr, DateTime defaultValue) {
  if (dateStr == null) return defaultValue;
  try {
    return DateTime.parse(dateStr);
  } catch (e) {
    debugPrint('Error parsing date: $e');
    return defaultValue;
  }
}

// Helper method to compare dates for sorting
int compareDates(
    Map<String, dynamic> a, Map<String, dynamic> b, String dateField) {
  final dateA = tryParseDate(a[dateField]?.toString());
  final dateB = tryParseDate(b[dateField]?.toString());

  if (dateA == null && dateB == null) return 0;
  if (dateA == null) return 1;
  if (dateB == null) return -1;

  return dateB.compareTo(dateA); // Sort by most recent first
}
*/

// END OF LEGACY PREGNANCY VIEW FILE
