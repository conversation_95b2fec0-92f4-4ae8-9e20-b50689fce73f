import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../services/transactions_repository.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../utils/message_utils.dart';

// --- Constants ---
class _AppStrings {
  static const String addTransactionTitle = 'Add Transaction';
  static const String editTransactionTitle = 'Edit Transaction';
  static const String dateLabel = 'Date';
  static const String transactionTypeLabel = 'Transaction Type';
  static const String categoryLabel = 'Category';
  static const String amountLabel = 'Amount';
  static const String paymentMethodLabel = 'Payment Method';
  static const String descriptionLabel = 'Description';

  // Validation messages
  static const String amountRequired = 'Amount is required';

  // Success messages
  static const String addSuccess = 'Transaction added successfully';
  static const String updateSuccess = 'Transaction updated successfully';
  static const String saveError = 'Error saving transaction';
}

class TransactionFormDialog extends StatefulWidget {
  final List<CategoryIsar> categories;
  final TransactionIsar? transaction;
  final VoidCallback? onTransactionAdded;

  const TransactionFormDialog({
    Key? key,
    required this.categories,
    this.transaction,
    this.onTransactionAdded,
  }) : super(key: key);

  @override
  State<TransactionFormDialog> createState() => _TransactionFormDialogState();
}

class _TransactionFormDialogState extends State<TransactionFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final TransactionsRepository _repository = GetIt.instance<TransactionsRepository>();

  late DateTime _selectedDate;
  late String _selectedType;
  String? _selectedCategory;
  late TextEditingController _amountController;
  late TextEditingController _descriptionController;
  String? _selectedPaymentMethod;
  bool _isSaving = false;
  bool _showOptionalFields = false;

  final List<String> _paymentMethods = [
    'Cash',
    'Credit Card',
    'Debit Card',
    'Bank Transfer',
    'Mobile Payment',
    'Check',
    'Other'
  ];

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.transaction?.date ?? DateTime.now();
    _selectedType = widget.transaction?.categoryType ?? 'Income';
    // For editing, use title field which contains the category name for display
    _selectedCategory = widget.transaction?.title;
    _amountController = TextEditingController(
      text: widget.transaction?.amount.toString() ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.transaction?.description ?? '',
    );
    _selectedPaymentMethod =
        widget.transaction?.paymentMethod ?? _paymentMethods[0];
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  List<String> _getCategoriesForType(String type) {
    return widget.categories
        .where((category) => category.type == type)
        .map((category) => category.name)
        .toList();
  }

  /// Get category businessId from category name
  String? _getCategoryBusinessId(String? categoryName) {
    if (categoryName == null) return null;
    final category = widget.categories.firstWhere(
      (cat) => cat.name == categoryName,
      orElse: () => CategoryIsar(),
    );
    return category.businessId;
  }





  void _handleTypeChange(String? newValue) {
    if (newValue != null) {
      setState(() {
        _selectedType = newValue;
        _selectedCategory = null;
      });
    }
  }

  Future<void> _handleSubmit() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isSaving = true);

      try {
        final transaction = widget.transaction ?? TransactionIsar();

        // Generate unique business ID for new transactions
        if (widget.transaction == null) {
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          transaction.transactionId = 'TXN-$timestamp';
          transaction.createdAt = DateTime.now();
        }

        // Get category businessId for proper linking
        final categoryBusinessId = _getCategoryBusinessId(_selectedCategory);

        // Update transaction fields
        transaction
          ..date = _selectedDate
          ..categoryType = _selectedType
          ..category = categoryBusinessId ?? '' // Store category businessId
          ..title = _selectedCategory ?? 'Transaction' // Store category name for display
          ..amount = double.parse(_amountController.text)
          ..description = _descriptionController.text
          ..paymentMethod = _selectedPaymentMethod ?? ''
          ..updatedAt = DateTime.now();

        // Save transaction using repository
        await _repository.saveTransaction(transaction);

        if (mounted) {
          MessageUtils.showSuccess(
            context,
            widget.transaction != null
                ? _AppStrings.updateSuccess
                : _AppStrings.addSuccess,
          );
          Navigator.of(context).pop();
          widget.onTransactionAdded?.call();
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, '${_AppStrings.saveError}: $e');
        }
      } finally {
        if (mounted) {
          setState(() => _isSaving = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.transaction == null
        ? UniversalFormDialog(
            title: _AppStrings.addTransactionTitle,
            headerIcon: Icons.account_balance_wallet, // Transaction-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSubmit,
              addText: 'Save', // User preference: Use 'Save' instead of 'Add'
              isAdding: _isSaving,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editTransactionTitle,
            headerIcon: Icons.account_balance_wallet, // Transaction-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSubmit,
              updateText: 'Save', // User preference: Use 'Save' instead of 'Update'
              isUpdating: _isSaving,
            ),
          );
  }

  Widget _buildFormContent() {
    final categoriesForType = _getCategoriesForType(_selectedType);

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
            // Date Field
            UniversalFormField.dateField(
              context: context,
              label: _AppStrings.dateLabel,
              value: _selectedDate,
              onChanged: (date) {
                setState(() {
                  _selectedDate = date ?? DateTime.now();
                });
              },
              prefixIcon: Icons.calendar_today,
              prefixIconColor: Colors.blue,
              lastDate: DateTime.now(),
            ),
            UniversalFormField.spacing,

            // Transaction Type Dropdown
            UniversalFormField.dropdownField<String>(
              label: _AppStrings.transactionTypeLabel,
              value: _selectedType,
              items: ['Income', 'Expense'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: _handleTypeChange,
              prefixIcon: _selectedType == 'Income' ? Icons.arrow_upward : Icons.arrow_downward,
              prefixIconColor: _selectedType == 'Income' ? Colors.green : Colors.red,
              validator: (value) => UniversalFormField.dropdownValidator(value, 'transaction type'),
            ),
            UniversalFormField.spacing,

            // Category Dropdown
            UniversalFormField.dropdownField<String>(
              label: _AppStrings.categoryLabel,
              value: _selectedCategory,
              items: categoriesForType.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedCategory = newValue;
                });
              },
              prefixIcon: Icons.category,
              prefixIconColor: Colors.purple,
              validator: (value) => UniversalFormField.dropdownValidator(value, 'category'),
            ),
            UniversalFormField.spacing,

            // Amount Field
            UniversalFormField.numberField(
              label: _AppStrings.amountLabel,
              controller: _amountController,
              allowDecimals: true,
              prefixIcon: Icons.attach_money,
              prefixIconColor: Colors.green,
              validator: (value) {
                if (value == null || value.isEmpty) return _AppStrings.amountRequired;
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) return 'Please enter a valid amount';
                return null;
              },
            ),
            UniversalFormField.spacing,

            // Optional Information Toggle Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  setState(() {
                    _showOptionalFields = !_showOptionalFields;
                  });
                },
                icon: Icon(
                  _showOptionalFields
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: const Color(0xFF2E7D32),
                ),
                label: Text(
                  _showOptionalFields
                      ? 'Hide Optional Information'
                      : 'Show Optional Information',
                  style: const TextStyle(
                    color: Color(0xFF2E7D32),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(
                    color: Color(0xFF2E7D32),
                    width: 1.5,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
            UniversalFormField.spacing,

            // Optional Fields Section
            if (_showOptionalFields) ...[
              // Payment Method Dropdown
              UniversalFormField.dropdownField<String>(
                label: _AppStrings.paymentMethodLabel,
                value: _selectedPaymentMethod,
                items: _paymentMethods.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedPaymentMethod = newValue;
                  });
                },
                prefixIcon: Icons.payment,
                prefixIconColor: Colors.indigo,
                validator: null, // Optional field, no validation required
              ),
              UniversalFormField.spacing,

              // Description Field
              UniversalFormField.textField(
                label: _AppStrings.descriptionLabel,
                controller: _descriptionController,
                prefixIcon: Icons.description,
                prefixIconColor: Colors.teal,
                validator: null, // Optional field, no validation required
              ),
              UniversalFormField.spacing,
            ],
        ],
      ),
    );
  }
}
