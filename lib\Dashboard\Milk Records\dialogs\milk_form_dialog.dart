import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../utils/message_utils.dart';

// --- Constants ---
class _AppStrings {
  static const String addMilkTitle = 'Add Milk Record';
  static const String editMilkTitle = 'Edit Milk Record';
  static const String cattleLabel = 'Select Cattle';
  static const String dateLabel = 'Date';
  static const String morningYieldLabel = 'Morning Yield (L)';
  static const String afternoonYieldLabel = 'Afternoon Yield (L)';
  static const String eveningYieldLabel = 'Evening Yield (L)';
  static const String notesLabel = 'Notes (Optional)';

  // Validation messages
  static const String yieldInvalid = 'Please enter a valid yield amount';

  // Success messages
  static const String addSuccess = 'Milk record added successfully';
  static const String updateSuccess = 'Milk record updated successfully';
  static const String saveError = 'Error saving milk record';
}

/// Dialog for adding/editing milk records
/// Follows the universal form dialog pattern with validation and cattle selection
class MilkFormDialog extends StatefulWidget {
  final MilkRecordIsar? milkRecord;
  final List<CattleIsar> cattle;
  final Function(MilkRecordIsar) onSave;

  const MilkFormDialog({
    super.key,
    this.milkRecord,
    required this.cattle,
    required this.onSave,
  });

  @override
  State<MilkFormDialog> createState() => _MilkFormDialogState();
}

class _MilkFormDialogState extends State<MilkFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _morningController = TextEditingController();
  final _afternoonController = TextEditingController();
  final _eveningController = TextEditingController();
  final _notesController = TextEditingController();

  CattleIsar? _selectedCattle;
  DateTime _selectedDate = DateTime.now();


  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.milkRecord != null) {
      final record = widget.milkRecord!;
      _morningController.text = record.morning?.toString() ?? '';
      _afternoonController.text = record.afternoon?.toString() ?? '';
      _eveningController.text = record.evening?.toString() ?? '';
      _notesController.text = record.notes ?? '';
      _selectedDate = record.date ?? DateTime.now();
      
      // Find selected cattle
      if (record.cattleBusinessId != null) {
        _selectedCattle = widget.cattle.firstWhere(
          (cattle) => cattle.businessId == record.cattleBusinessId,
          orElse: () => widget.cattle.first,
        );
      }
    }
  }

  @override
  void dispose() {
    _morningController.dispose();
    _afternoonController.dispose();
    _eveningController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.milkRecord == null
        ? UniversalFormDialog(
            title: _AppStrings.addMilkTitle,
            headerIcon: Icons.water_drop, // Milk-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editMilkTitle,
            headerIcon: Icons.water_drop, // Milk-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Cattle Selection
          UniversalFormField.dropdownField<CattleIsar>(
            label: _AppStrings.cattleLabel,
            value: _selectedCattle,
            items: widget.cattle.map((cattle) {
              final cattleName = cattle.name ?? 'Unknown';
              final tagId = cattle.tagId ?? '';
              final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
              return DropdownMenuItem(
                value: cattle,
                child: Text(displayName, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (cattle) {
              setState(() {
                _selectedCattle = cattle;
              });
            },
            prefixIcon: Icons.pets,
            prefixIconColor: Colors.green, // Changed from brown (forbidden) to green
            validator: (value) => UniversalFormField.dropdownValidator(value, 'cattle'),
          ),
          UniversalFormField.spacing,

          // Date Selection
          UniversalFormField.dateField(
            context: context,
            label: _AppStrings.dateLabel,
            value: _selectedDate,
            onChanged: (date) {
              setState(() {
                _selectedDate = date ?? DateTime.now();
              });
            },
            prefixIcon: Icons.calendar_today,
            prefixIconColor: Colors.blue,
            lastDate: DateTime.now(),
          ),
          UniversalFormField.spacing,

          // Morning Yield Field
          UniversalFormField.numberField(
            label: _AppStrings.morningYieldLabel,
            controller: _morningController,
            allowDecimals: true,
            prefixIcon: Icons.wb_sunny,
            prefixIconColor: Colors.red, // Changed from orange (forbidden) to red
            validator: (value) {
              if (value?.isNotEmpty == true) {
                final amount = double.tryParse(value!);
                if (amount == null || amount < 0) {
                  return _AppStrings.yieldInvalid;
                }
              }
              return null;
            },
          ),
          UniversalFormField.spacing,

          // Afternoon Yield Field
          UniversalFormField.numberField(
            label: _AppStrings.afternoonYieldLabel,
            controller: _afternoonController,
            allowDecimals: true,
            prefixIcon: Icons.wb_cloudy,
            prefixIconColor: Colors.blue,
            validator: (value) {
              if (value?.isNotEmpty == true) {
                final amount = double.tryParse(value!);
                if (amount == null || amount < 0) {
                  return _AppStrings.yieldInvalid;
                }
              }
              return null;
            },
          ),
          UniversalFormField.spacing,

          // Evening Yield Field
          UniversalFormField.numberField(
            label: _AppStrings.eveningYieldLabel,
            controller: _eveningController,
            allowDecimals: true,
            prefixIcon: Icons.nights_stay,
            prefixIconColor: Colors.indigo,
            validator: (value) {
              if (value?.isNotEmpty == true) {
                final amount = double.tryParse(value!);
                if (amount == null || amount < 0) {
                  return _AppStrings.yieldInvalid;
                }
              }
              return null;
            },
          ),
          UniversalFormField.spacing,

          // Notes Field
          UniversalFormField.multilineField(
            label: _AppStrings.notesLabel,
            controller: _notesController,
            maxLines: 3,
            prefixIcon: Icons.notes,
            prefixIconColor: Colors.teal,
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCattle == null) return;



    try {
      await _saveMilkRecord();

      if (mounted) {
        MilkMessageUtils.showSuccess(
          context,
          widget.milkRecord != null
              ? _AppStrings.updateSuccess
              : _AppStrings.addSuccess,
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        MilkMessageUtils.showError(context, '${_AppStrings.saveError}: $e');
      }
    } finally {
      if (mounted) {

      }
    }
  }

  Future<void> _saveMilkRecord() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate that at least one milk amount is provided
    final morning = double.tryParse(_morningController.text) ?? 0;
    final afternoon = double.tryParse(_afternoonController.text) ?? 0;
    final evening = double.tryParse(_eveningController.text) ?? 0;

    if (morning == 0 && afternoon == 0 && evening == 0) {
      MilkMessageUtils.showError(context, 'Please enter at least one milk amount');
      return;
    }



    try {
      final record = widget.milkRecord ?? MilkRecordIsar();
      
      record.cattleBusinessId = _selectedCattle!.businessId;
      record.cattleTagId = _selectedCattle!.tagId;
      record.date = _selectedDate;
      record.morning = morning > 0 ? morning : null;
      record.afternoon = afternoon > 0 ? afternoon : null;
      record.evening = evening > 0 ? evening : null;
      record.totalAmount = morning + afternoon + evening; // Calculate and store total

      record.notes = _notesController.text.isNotEmpty ? _notesController.text : null;
      record.updatedAt = DateTime.now();
      
      if (widget.milkRecord == null) {
        record.createdAt = DateTime.now();
      }

      widget.onSave(record);
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving milk record: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {

      }
    }
  }
}
