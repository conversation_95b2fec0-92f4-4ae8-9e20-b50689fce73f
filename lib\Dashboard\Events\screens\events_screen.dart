import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import '../controllers/events_controller.dart';
import '../dialogs/event_form_dialog.dart';
import '../tabs/events_records_tab.dart';
import '../tabs/events_analytics_tab.dart';
import '../tabs/events_insights_tab.dart';
import '../services/events_insights_service.dart';
import '../../../routes/app_routes.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum

 // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart'; // Import Universal Tabs


/// Events screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class EventsScreen extends StatelessWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => EventsController(),
      child: const _EventsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _EventsScreenContent extends StatefulWidget {
  const _EventsScreenContent();

  @override
  State<_EventsScreenContent> createState() => _EventsScreenContentState();
}

class _EventsScreenContentState extends State<_EventsScreenContent>
    with TickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Events Management',
      body: Consumer<EventsController>(
        builder: (context, eventsController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.threeTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => EventsAnalyticsTab(controller: eventsController),
              ),
              Builder(
                builder: (context) => const EventsRecordsTab(), // Uses Provider pattern
              ),
              Builder(
                builder: (context) => EventsInsightsTab(
                  // Ultimate Pure Dependency Injection: ALL dependencies provided by parent
                  // Widget has ZERO knowledge of dependency creation - perfect architectural purity
                  controller: eventsController,
                  insightsService: GetIt.instance<EventsInsightsService>(),
                ),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Insights'],
            icons: const [Icons.analytics, Icons.list, Icons.lightbulb],
            showFABs: const [false, true, false], // FAB only on Records tab
          );

          // Handle different states
          if (eventsController.state == ControllerState.loading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (eventsController.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    eventsController.errorMessage ?? 'Failed to load events data',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {}, // No manual retry needed - reactive streams auto-recover
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return _tabManager!;
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.eventsReport,
          ),
          tooltip: 'View Events Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _addEvent,
            tooltip: 'Add Event',
            backgroundColor: AppColors.eventsHeader,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  void _addEvent() {
    final eventsController = context.read<EventsController>();

    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattleId: '', // Default empty - user will select cattle
        onSave: (event) {
          eventsController.addEvent(event);
        },
      ),
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}
