import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../controllers/breeding_details_controller.dart';
import '../models/delivery_record_isar.dart';
import '../dialogs/delivery_form_dialog.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../utils/message_utils.dart';
import '../../../Dashboard/widgets/universal_record_card.dart';
import '../../../Dashboard/Cattle/widgets/eligibility_card.dart';
import '../../../Dashboard/Cattle/widgets/stats_card.dart';



class BreedingDetailsDeliveryTab extends StatefulWidget {
  const BreedingDetailsDeliveryTab({super.key});

  @override
  State<BreedingDetailsDeliveryTab> createState() => _BreedingDetailsDeliveryTabState();
}

class _BreedingDetailsDeliveryTabState extends State<BreedingDetailsDeliveryTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Consumer<BreedingDetailsController>(
      builder: (context, controller, child) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        final deliveryRecords = controller.deliveryRecords;

        if (deliveryRecords.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Delivery Records',
            message: 'No deliveries recorded for ${controller.cattle?.name ?? 'this cattle'}.',
            tabColor: AppColors.breedingKpiColors[0],
            tabIndex: 0,
            action: TabEmptyStateActions.addFirstRecord(
              onPressed: () => _addDeliveryRecord(context, controller),
              tabColor: AppColors.breedingKpiColors[0],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80), // Bottom padding for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Stats card at top
              if (deliveryRecords.isNotEmpty) ...[
                _buildStatsCard(deliveryRecords),
                const SizedBox(height: 16),
              ],
              // Status/eligibility card
              if (controller.cattle != null) ...[
                _buildEligibilityCard(controller),
                const SizedBox(height: 16),
              ],
              // Records list
              _buildRecordsList(deliveryRecords),
            ],
          ),
        );
      },
    );
  }



  Widget _buildRecordsList(List<DeliveryRecordIsar> records) {
    // Sort records by delivery date (most recent first)
    final sortedRecords = List<DeliveryRecordIsar>.from(records)
      ..sort((a, b) => (b.deliveryDate ?? DateTime.now()).compareTo(a.deliveryDate ?? DateTime.now()));

    return Column(
      children: sortedRecords.map((record) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: _buildRecordCard(record),
      )).toList(),
    );
  }

  Widget _buildRecordCard(DeliveryRecordIsar record) {
    // Format dates
    String deliveryDateText = record.deliveryDate != null
        ? DateFormat('MMM dd, yyyy').format(record.deliveryDate!)
        : 'Unknown date';

    // Format calf count and delivery type
    String calfCountText = '${record.calfCount} ${record.calfCount == 1 ? 'Calf' : 'Calves'}';
    String deliveryTypeText = record.deliveryType ?? 'Unknown type';

    // Format complications and veterinarian for row 3
    String complicationsText = record.hadComplications
        ? (record.complicationDetails?.isNotEmpty == true
            ? 'Complications: ${record.complicationDetails}'
            : 'Had complications')
        : 'No complications';
    String veterinarianText = record.veterinarian?.isNotEmpty == true
        ? 'Vet: ${record.veterinarian}'
        : 'No vet assistance';

    return UniversalRecordCard(
      row1Left: deliveryDateText,
      row1Right: calfCountText,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.child_care,
      row2Left: deliveryTypeText,
      row2Right: record.status ?? 'Completed',
      row2LeftIcon: Icons.medical_services,
      row2RightIcon: Icons.check_circle,
      row3Left: complicationsText,
      row3Right: veterinarianText,
      row3LeftIcon: record.hadComplications ? Icons.warning : Icons.check_circle_outline,
      row3RightIcon: record.veterinarian?.isNotEmpty == true ? Icons.medical_services : Icons.person_off,
      notes: _buildNotesText(record),
      primaryColor: AppColors.breedingHeader,
      onTap: () => _viewRecordDetails(record),
      onEdit: () => _editRecord(record),
      onDelete: () => _deleteRecord(record),
    );
  }

  String? _buildNotesText(DeliveryRecordIsar record) {
    List<String> notesParts = [];

    // Add regular notes
    if (record.notes?.isNotEmpty == true) {
      notesParts.add(record.notes!);
    }

    // Add calf details if available
    if (record.calfDetailsJson?.isNotEmpty == true) {
      notesParts.add('Calf Details: ${record.calfDetailsJson}');
    }

    return notesParts.isNotEmpty ? notesParts.join('\n\n') : null;
  }

  Widget _buildEligibilityCard(BreedingDetailsController controller) {
    final cattle = controller.cattle!;

    // Check if cattle is currently pregnant (would affect delivery eligibility)
    final hasActivePregnancy = controller.pregnancyRecords.any(
      (record) => record.status?.toLowerCase() == 'confirmed'
    );

    // Get the most recent delivery for context
    final lastDeliveryDate = controller.deliveryRecords.isNotEmpty
        ? controller.deliveryRecords.first.deliveryDate
        : null;

    return EligibilityCard.breeding(
      gender: cattle.gender.name,
      cattleId: cattle.businessId ?? '',
      animalTypeId: cattle.animalTypeId ?? '',
      isPregnant: hasActivePregnancy,
      dateOfBirth: cattle.dateOfBirth,
      purchaseDate: cattle.purchaseDate,
      lastCalvingDate: lastDeliveryDate,
      lastBreedingDate: controller.breedingRecords.isNotEmpty
          ? controller.breedingRecords.first.date
          : null,
      onAddPressed: () => _addDeliveryRecord(context, controller),
    );
  }

  Widget _buildStatsCard(List<DeliveryRecordIsar> deliveryRecords) {
    // Calculate delivery statistics
    final totalDeliveries = deliveryRecords.length;
    final totalCalves = deliveryRecords.fold<int>(0, (sum, record) => sum + record.calfCount);
    final deliveriesWithComplications = deliveryRecords.where((r) => r.hadComplications).length;
    final complicationRate = totalDeliveries > 0 ? (deliveriesWithComplications / totalDeliveries * 100) : 0.0;
    final avgCalvesPerDelivery = totalDeliveries > 0 ? (totalCalves / totalDeliveries) : 0.0;

    // Count delivery types
    final normalDeliveries = deliveryRecords.where((r) => r.deliveryType?.toLowerCase() == 'normal').length;

    return StatsCard(
      title: 'Delivery Statistics',
      titleColor: AppColors.breedingHeader,
      titleIcon: Icons.child_care,
      successRate: 100 - complicationRate, // Success rate is inverse of complication rate
      successRateLabel: 'Success Rate',
      statItems: [
        {
          'label': 'Total Deliveries',
          'value': totalDeliveries.toString(),
          'icon': Icons.child_care,
          'color': Colors.blue,
        },
        {
          'label': 'Total Calves',
          'value': totalCalves.toString(),
          'icon': Icons.pets,
          'color': Colors.green,
        },
        {
          'label': 'Normal Births',
          'value': normalDeliveries.toString(),
          'icon': Icons.check_circle,
          'color': Colors.purple,
        },
        {
          'label': 'Complications',
          'value': deliveriesWithComplications.toString(),
          'icon': Icons.warning,
          'color': Colors.red,
        },
        {
          'label': 'Avg Calves',
          'value': avgCalvesPerDelivery.toStringAsFixed(1),
          'icon': Icons.trending_up,
          'color': Colors.orange,
        },
      ],
    );
  }

  void _addDeliveryRecord(BuildContext context, BreedingDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => DeliveryFormDialog(
        existingCattle: [controller.cattle!],
        motherTagId: controller.cattle!.tagId,
        onSave: (record) async {
          final success = await controller.addDeliveryRecord(record);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Delivery record added successfully');
          }
        },
      ),
    );
  }

  void _viewRecordDetails(DeliveryRecordIsar record) {
    // TODO: Implement record details view
    MessageUtils.showInfo(context, 'Record details view not yet implemented');
  }

  // Removed unused _handleMenuAction method

  void _editRecord(DeliveryRecordIsar record) {
    final controller = Provider.of<BreedingDetailsController>(context, listen: false);
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => DeliveryFormDialog(
        record: record,
        existingCattle: [controller.cattle!],
        motherTagId: controller.cattle!.tagId,
        onSave: (updatedRecord) async {
          final success = await controller.updateDeliveryRecord(updatedRecord);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Delivery record updated successfully');
          }
        },
      ),
    );
  }

  void _deleteRecord(DeliveryRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Delivery Record'),
        content: const Text('Are you sure you want to delete this delivery record? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final controller = Provider.of<BreedingDetailsController>(context, listen: false);
              final success = await controller.deleteDeliveryRecord(record.businessId!);
              if (!mounted) return;
              if (success) {
                MessageUtils.showSuccess(context, 'Delivery record deleted successfully');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
