import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/milk_record_isar.dart';
import '../dialogs/milk_form_dialog.dart';
import '../details/milk_details_screen.dart';
import '../../widgets/index.dart';
import '../controllers/milk_controller.dart';
// For ControllerState
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_constants.dart';

import 'package:intl/intl.dart';

/// Milk Records Tab - Shows list of all milk records with filtering and search
/// Follows the cattle records tab pattern exactly
class MilkRecordsTab extends StatefulWidget {
  final MilkController? controller; // Made optional to support Provider pattern

  const MilkRecordsTab({
    Key? key,
    this.controller, // Optional - will use Provider if not provided
  }) : super(key: key);

  @override
  State<MilkRecordsTab> createState() => _MilkRecordsTabState();
}

class _MilkRecordsTabState extends State<MilkRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  MilkController get _controller => widget.controller ?? context.read<MilkController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    // Apply filters at database level for ultimate scalability
    _controller.applyFilters(_filterController.toMilkFilterState());
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    // Check if we have data to display
    if (_controller.totalMilkRecords == 0) {
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    // Get milk records data - now pre-filtered at database level
    final records = _controller.milkRecords;
    final allRecordsCount = _controller.totalMilkRecords; // This represents total before filtering

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.milk,
          moduleName: 'milk',
          sortFields: const [...SortField.commonFields, ...SortField.milkFields],
          searchHint: 'Search milk records by cattle, date, or session...',
          totalCount: allRecordsCount,
          filteredCount: records.length,
        ),

        // Milk Records List - data is already filtered at database level
        Expanded(
          child: records.isEmpty
              ? _buildEmptyState(allRecordsCount == 0)
              : RefreshIndicator(
                  onRefresh: () async {
                    // Enhanced pull-to-refresh: refresh data AND clear filters for full reset
                    // UX Decision: Pull-to-refresh acts as a complete reset, clearing filters
                    // to provide users with a "fresh start" experience. This is intuitive
                    // behavior when users want to see all data without current filter constraints.
                    await _controller.refresh();
                    _filterController.clearAllApplied();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: records.length,
                    itemBuilder: (context, index) {
                      final record = records[index];
                      return _buildMilkRecordCard(record);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  /// Consolidated empty state builder - single source of truth for all empty states
  ///
  /// [isCompletelyEmpty] - true when no milk records exist at all, false when records exist but filters hide them
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    // Get the tab color for Records tab (index 1) from milk module
    const tabColor = AppColors.milkHeader;

    if (isCompletelyEmpty) {
      // No milk records exist - show call-to-action to add first record
      return UniversalTabEmptyState.forTab(
        title: 'No Milk Records',
        message: 'Add your first milk record to start tracking production.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddMilkDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Milk records exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Milk Records',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildMilkRecordCard(MilkRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleBusinessId);

    // Format row 1: Date + Total amount
    String dateText = _formatDate(record.date);
    String totalAmount = '${record.totalYield.toStringAsFixed(1)}L';

    // Format row 2: Cattle name with tagId and session info
    String cattleName = cattle?.name ?? 'Unknown Cattle';
    if (cattle?.tagId != null && cattle!.tagId!.isNotEmpty) {
      cattleName += ' (${cattle.tagId!.toUpperCase()})';
    }
    String sessionInfo = _getSessionInfo(record);

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: totalAmount,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.water_drop,
      row2Left: cattleName,
      row2Right: sessionInfo,
      row2LeftIcon: Icons.pets,
      row2RightIcon: Icons.schedule,
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.milkHeader,
      onTap: () => _navigateToMilkDetail(record),
      onEdit: () => _showEditMilkDialog(record),
      onDelete: () => _showDeleteConfirmation(record),
    );
  }

  String _getSessionInfo(MilkRecordIsar record) {
    final sessions = <String>[];
    if ((record.morningAmount ?? 0) > 0) sessions.add('M');
    if ((record.afternoonAmount ?? 0) > 0) sessions.add('A');
    if ((record.eveningAmount ?? 0) > 0) sessions.add('E');

    if (sessions.isEmpty) return 'No sessions';
    return sessions.join(', '); // Remove "Sessions:" label, just show values
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Consolidated dialog helper for both add and edit operations
  ///
  /// [record] - null for add operation, existing record for edit operation
  void _showMilkRecordFormDialog([MilkRecordIsar? record]) {
    final isEditing = record != null;

    showDialog(
      context: context,
      builder: (context) => MilkFormDialog(
        milkRecord: record, // null for add, existing record for edit
        cattle: _controller.cattle,
        onSave: (recordData) async {
          if (isEditing) {
            await _controller.updateMilkRecord(recordData);
          } else {
            await _controller.addMilkRecord(recordData);
          }
        },
      ),
    );
  }

  /// Show dialog to add new milk record
  void _showAddMilkDialog() => _showMilkRecordFormDialog();

  /// Show dialog to edit existing milk record
  void _showEditMilkDialog(MilkRecordIsar record) => _showMilkRecordFormDialog(record);

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }

  void _navigateToMilkDetail(MilkRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleBusinessId);
    if (cattle != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MilkDetailsScreen(cattle: cattle),
        ),
      );
    }
  }

  void _showDeleteConfirmation(MilkRecordIsar record) {
    final cattle = _controller.getCattle(record.cattleBusinessId);
    final recordDescription = 'milk record for ${cattle?.name ?? 'Unknown cattle'} on ${_formatDate(record.date)}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Milk Record'),
        content: Text('Are you sure you want to delete this $recordDescription?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
          await _controller.deleteMilkRecord(record.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
