import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/events_controller.dart';
import '../models/event_isar.dart';
import '../../widgets/index.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_constants.dart';

import 'package:intl/intl.dart';

/// Events Records Tab - Shows list of all events with filtering and search
/// Follows the cattle records tab pattern exactly
class EventsRecordsTab extends StatefulWidget {
  final EventsController? controller; // Made optional to support Provider pattern

  const EventsRecordsTab({
    super.key,
    this.controller, // Optional - will use Provider if not provided
  });

  @override
  State<EventsRecordsTab> createState() => _EventsRecordsTabState();
}

class _EventsRecordsTabState extends State<EventsRecordsTab> {
  late FilterController _filterController;

  /// Get controller from either widget prop or Provider
  EventsController get _controller => widget.controller ?? context.read<EventsController>();

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    // Listen for filter changes to apply database-side filtering
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  /// Handle filter changes by applying them at the database level
  void _onFiltersChanged() {
    _controller.applyFilters(_filterController.toEventFilterState());
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<EventsController>(
      builder: (context, controller, child) {
        return Column(
          children: [
            // Universal Filter Layout
            UniversalFilterLayout(
              controller: _filterController,
              theme: FilterTheme.events,
              moduleName: 'events',
              sortFields: const [...SortField.commonFields, ...SortField.eventFields],
              searchHint: 'Search events by title, description, or cattle...',
              totalCount: controller.totalEvents,
              filteredCount: controller.filteredEvents.length,
            ),

            // Records List
            Expanded(
              child: _buildRecordsList(context, controller),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecordsList(BuildContext context, EventsController controller) {
    final events = controller.filteredEvents;

    if (events.isEmpty) {
      const tabColor = AppColors.eventsHeader;
      return UniversalTabEmptyState.forTab(
        title: 'No Event Records',
        message: 'Add your first event to start managing your schedule.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddEventDialog(),
          tabColor: tabColor,
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(kPaddingMedium),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return _buildEventCard(event);
      },
    );
  }


  String _formatDate(DateTime? date) {
    if (date == null) return 'No date';
    return DateFormat('MMM dd, yyyy').format(date);
  }

  String _getStatusText(EventIsar event) {
    if (event.isCompleted) return 'Completed';
    if (event.dueDate != null && event.dueDate!.isBefore(DateTime.now())) {
      return 'Overdue';
    }
    return 'Pending';
  }

  void _navigateToEventDetail(EventIsar event) {
    // Details screen not implemented yet
  }

  void _showEditEventDialog(EventIsar event) {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattleId: event.cattleId ?? '',
        event: event,
        onSave: (updatedEvent) async {
          await _controller.updateEvent(updatedEvent);
        },
      ),
    );
  }

  void _showDeleteConfirmation(EventIsar event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _controller.deleteEvent(event.id);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildEventCard(EventIsar event) {
    // Format event name and type
    String eventName = event.title ?? 'Untitled Event';
    String eventType = event.type?.getDisplayName() ?? 'Unknown';
    String eventDate = _formatDate(event.eventDate);
    String eventStatus = _getStatusText(event);

    return UniversalRecordCard(
      row1Left: eventName,
      row1Right: eventDate,
      row1LeftIcon: Icons.event,
      row1RightIcon: Icons.calendar_today,
      row2Left: eventType,
      row2Right: eventStatus,
      row2LeftIcon: Icons.category,
      row2RightIcon: Icons.info,
      notes: event.notes?.isNotEmpty == true ? event.notes : null,
      primaryColor: AppColors.eventsHeader,
      onTap: () => _navigateToEventDetail(event),
      onEdit: () => _showEditEventDialog(event),
      onDelete: () => _showDeleteConfirmation(event),
    );
  }

  void _showAddEventDialog() {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattleId: '', // Default empty - user will select cattle
        onSave: (event) async {
          await _controller.addEvent(event);
        },
      ),
    );
  }

  // Unused methods removed
}

