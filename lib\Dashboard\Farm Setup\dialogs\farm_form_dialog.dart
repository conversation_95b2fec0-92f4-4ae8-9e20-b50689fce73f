import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/farm_isar.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:logging/logging.dart';
import '../../../constants/app_layout.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../utils/message_utils.dart';

// --- Constants ---
class _AppStrings {
  static const String addFarmTitle = 'Add Farm';
  static const String editFarmTitle = 'Edit Farm';
  static const String farmNameLabel = 'Farm Name';
  static const String farmTypeLabel = 'Farm Type';
  static const String ownerNameLabel = 'Owner Name';
  static const String ownerContactLabel = 'Owner Contact';
  static const String ownerEmailLabel = 'Owner Email';
  static const String addressLabel = 'Address';
  static const String cattleCountLabel = 'Current Cattle Count';
  static const String capacityLabel = 'Farm Capacity';

  // Validation messages
  static const String cattleCountInvalid = 'Please enter a valid cattle count';
  static const String capacityInvalid = 'Please enter a valid capacity';

  // Success messages
  static const String addSuccess = 'Farm added successfully';
  static const String updateSuccess = 'Farm updated successfully';
  static const String saveError = 'Error saving farm';
}

class FarmFormDialog extends StatefulWidget {
  final FarmIsar? farm;

  const FarmFormDialog({Key? key, this.farm}) : super(key: key);

  @override
  State<FarmFormDialog> createState() => _FarmFormDialogState();
}

class _FarmFormDialogState extends State<FarmFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _ownerContactController = TextEditingController();
  final _ownerEmailController = TextEditingController();
  final _addressController = TextEditingController();
  final _cattleCountController = TextEditingController();
  final _capacityController = TextEditingController();
  final _logger = Logger('FarmFormDialog');

  FarmType _selectedFarmType = FarmType.mixed;
  double? _latitude;
  double? _longitude;
  bool _isFetchingLocation = false;

  void _showError(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    if (widget.farm != null) {
      _nameController.text = widget.farm!.name ?? '';
      _ownerNameController.text = widget.farm!.ownerName ?? '';
      _ownerContactController.text = widget.farm!.ownerContact ?? '';
      _ownerEmailController.text = widget.farm!.ownerEmail ?? '';
      _addressController.text = widget.farm!.address ?? '';
      _cattleCountController.text = widget.farm!.cattleCount?.toString() ?? '0';
      _capacityController.text = widget.farm!.capacity?.toString() ?? '0';
      _selectedFarmType = widget.farm!.farmType;
      _latitude = widget.farm!.latitude;
      _longitude = widget.farm!.longitude;
    } else {
      _cattleCountController.text = '0';
      _capacityController.text = '0';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ownerNameController.dispose();
    _ownerContactController.dispose();
    _ownerEmailController.dispose();
    _addressController.dispose();
    _cattleCountController.dispose();
    _capacityController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    if (_isFetchingLocation) return; // Prevent concurrent requests

    setState(() => _isFetchingLocation = true);

    try {
      _logger.info("Starting location retrieval process");

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      _logger.info("Location services enabled: $serviceEnabled");

      if (!serviceEnabled) {
        // Location services are not enabled
        _showError(
            'Location services are disabled. Please enable location services.');
        return;
      }

      // Check location permission
      _logger.info("Checking location permission");
      LocationPermission permission = await Geolocator.checkPermission();
      _logger.info("Current permission status: $permission");

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.unableToDetermine) {
        _logger.info(
            "Permission denied or unable to determine, requesting permission");
        permission = await Geolocator.requestPermission();
        _logger.info("After request, permission status: $permission");

        if (permission == LocationPermission.denied) {
          _showError(
              'Location permission denied. Please grant access in Settings.');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showError(
            'Location permission permanently denied. Please enable in Settings > Apps > Cattle Manager.');
        return;
      }

      _logger.info("Permission granted, getting current position");

      // Use a simpler approach with less timeout complexity
      try {
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 10),
        );

        if (!mounted) return;

        _logger.info(
            "Position obtained: ${position.latitude}, ${position.longitude}");
        setState(() {
          _latitude = position.latitude;
          _longitude = position.longitude;
        });

        // Try reverse geocoding to get address from coordinates
        try {
          _logger.info("Starting reverse geocoding");
          List<Placemark> placemarks = await placemarkFromCoordinates(
            position.latitude,
            position.longitude,
          ).catchError((error) {
            _logger.warning("Error in placemarkFromCoordinates: $error");
            return <Placemark>[];
          });

          if (placemarks.isNotEmpty) {
            Placemark place = placemarks[0];
            _logger.info("Raw placemark data: ${_placemarkToString(place)}");

            // Use a safer approach to format the address
            String address = _safeFormatAddress(
                place, position.latitude, position.longitude);
            _logger.info("Formatted address: $address");

            if (mounted) {
              setState(() {
                _addressController.text = address;
              });
            }
          } else {
            _logger.warning("No placemarks found for coordinates");
            if (mounted) {
              setState(() {
                _addressController.text =
                    "Location: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}";
              });
            }
          }
        } catch (error) {
          _logger.warning("Error in reverse geocoding: $error");
          if (mounted) {
            setState(() {
              _addressController.text =
                  "Location: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}";
            });
          }
        }
      } catch (locationError) {
        _logger.warning("Error getting position: $locationError");

        // Try getting last known position as fallback
        _logger.info("Trying to get last known position instead");
        try {
          final lastPosition = await Geolocator.getLastKnownPosition();
          if (lastPosition != null && mounted) {
            _logger.info(
                "Last position obtained: ${lastPosition.latitude}, ${lastPosition.longitude}");
            setState(() {
              _latitude = lastPosition.latitude;
              _longitude = lastPosition.longitude;
            });

            // Try reverse geocoding with last known position
            try {
              List<Placemark> placemarks = await placemarkFromCoordinates(
                lastPosition.latitude,
                lastPosition.longitude,
              ).catchError((error) {
                _logger.warning(
                    "Error in placemarkFromCoordinates for last position: $error");
                return <Placemark>[];
              });

              if (placemarks.isNotEmpty && mounted) {
                Placemark place = placemarks[0];
                _logger.info(
                    "Last position placemark data: ${_placemarkToString(place)}");

                // Use the safer method to format address
                String address = _safeFormatAddress(
                    place, lastPosition.latitude, lastPosition.longitude);
                _logger.info("Last position formatted address: $address");

                setState(() {
                  _addressController.text = address;
                });
              } else if (mounted) {
                setState(() {
                  _addressController.text =
                      "Location: ${lastPosition.latitude.toStringAsFixed(6)}, ${lastPosition.longitude.toStringAsFixed(6)}";
                });
              }
            } catch (e) {
              _logger.warning(
                  "Error in reverse geocoding last known position: $e");
              if (mounted) {
                setState(() {
                  _addressController.text =
                      "Location: ${lastPosition.latitude.toStringAsFixed(6)}, ${lastPosition.longitude.toStringAsFixed(6)}";
                });
              }
            }

            _showError(
                'Using last known location. For better accuracy, try again outdoors.');
          } else {
            throw Exception("No location available");
          }
        } catch (e) {
          _logger.severe("Failed to get last known position: $e");
          _showError(
              'Could not determine location. Please try again or enter address manually.');
        }
      }
    } catch (e) {
      _logger.severe("Error in _getCurrentLocation: $e");
      _showError('Error getting location. Please try again later.');
    } finally {
      if (mounted) {
        setState(() => _isFetchingLocation = false);
      }
    }
  }

  // Helper method to format address from Placemark

  // Helper method to safely format address from Placemark with fallback
  String _safeFormatAddress(
      Placemark place, double latitude, double longitude) {
    // Create a list to hold non-empty address components
    List<String> addressParts = [];

    // Try to add each non-null and non-empty component
    // Using safe access pattern for each property
    if (place.name != null && place.name!.isNotEmpty) {
      addressParts.add(place.name!);
    }

    if (place.street != null && place.street!.isNotEmpty) {
      addressParts.add(place.street!);
    }

    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }

    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }

    if (place.administrativeArea != null &&
        place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }

    if (place.postalCode != null && place.postalCode!.isNotEmpty) {
      addressParts.add(place.postalCode!);
    }

    if (place.country != null && place.country!.isNotEmpty) {
      addressParts.add(place.country!);
    }

    // If we got address parts, return them joined by commas
    if (addressParts.isNotEmpty) {
      return addressParts.join(', ');
    }

    // Fallback to coordinates if no address parts found
    return "Location: ${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}";
  }

  // Helper to create a readable string of all placemark fields for debugging
  String _placemarkToString(Placemark place) {
    return '''
    name: ${place.name}, 
    street: ${place.street}, 
    isoCountryCode: ${place.isoCountryCode}, 
    country: ${place.country}, 
    postalCode: ${place.postalCode}, 
    administrativeArea: ${place.administrativeArea}, 
    subAdministrativeArea: ${place.subAdministrativeArea}, 
    locality: ${place.locality}, 
    subLocality: ${place.subLocality}, 
    thoroughfare: ${place.thoroughfare}, 
    subThoroughfare: ${place.subThoroughfare}
    ''';
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      FarmIsar farmToSave;

      if (widget.farm != null) {
        // Editing existing farm - create a new object but preserve the Isar ID
        farmToSave = FarmIsar.create(
          id: widget.farm!.farmBusinessId ?? const Uuid().v4(),
          name: _nameController.text,
          ownerName: _ownerNameController.text,
          ownerContact: _ownerContactController.text,
          ownerEmail: _ownerEmailController.text,
          latitude: _latitude,
          longitude: _longitude,
          address: _addressController.text,
          farmType: _selectedFarmType,
          cattleCount: int.tryParse(_cattleCountController.text) ??
              widget.farm!.cattleCount ??
              0,
          capacity: int.tryParse(_capacityController.text) ??
              widget.farm!.capacity ??
              0,
          createdAt: widget.farm!.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Preserve the internal Isar ID for update
        farmToSave.id = widget.farm!.id;
      } else {
        // Creating a new farm
        farmToSave = FarmIsar.create(
          id: const Uuid().v4(), // This should map to farmBusinessId
          name: _nameController.text,
          ownerName: _ownerNameController.text,
          ownerContact: _ownerContactController.text,
          ownerEmail: _ownerEmailController.text,
          latitude: _latitude,
          longitude: _longitude,
          address: _addressController.text,
          farmType: _selectedFarmType,
          cattleCount: int.tryParse(_cattleCountController.text) ?? 0,
          capacity: int.tryParse(_capacityController.text) ?? 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }

      if (!mounted) return;
      Navigator.of(context).pop(farmToSave);
    } catch (e) {
      _logger.severe("Error creating/updating farm: $e");
      _showError(
          'Failed to save farm details. Please check your inputs and try again.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.farm == null
        ? UniversalFormDialog(
            title: _AppStrings.addFarmTitle,
            headerIcon: Icons.agriculture, // Farm-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editFarmTitle,
            headerIcon: Icons.agriculture, // Farm-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Farm Name Field
          UniversalFormField.textField(
            label: _AppStrings.farmNameLabel,
            controller: _nameController,
            prefixIcon: Icons.business,
            prefixIconColor: Colors.green,
            validator: (value) => UniversalFormField.requiredValidator(value, 'farm name'),
          ),
          UniversalFormField.spacing,

          // Farm Type Dropdown
          UniversalFormField.dropdownField<FarmType>(
            label: _AppStrings.farmTypeLabel,
            value: _selectedFarmType,
            items: FarmType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type.name),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedFarmType = value!;
              });
            },
            prefixIcon: Icons.category,
            prefixIconColor: Colors.blue,
          ),
          UniversalFormField.spacing,

          // Owner Name Field
          UniversalFormField.textField(
            label: _AppStrings.ownerNameLabel,
            controller: _ownerNameController,
            prefixIcon: Icons.person,
            prefixIconColor: Colors.purple,
            validator: (value) => UniversalFormField.requiredValidator(value, 'owner name'),
          ),
          UniversalFormField.spacing,

          // Owner Contact Field
          UniversalFormField.textField(
            label: _AppStrings.ownerContactLabel,
            controller: _ownerContactController,
            prefixIcon: Icons.phone,
            prefixIconColor: Colors.orange,
          ),
          UniversalFormField.spacing,

          // Owner Email Field
          UniversalFormField.textField(
            label: _AppStrings.ownerEmailLabel,
            controller: _ownerEmailController,
            prefixIcon: Icons.email,
            prefixIconColor: Colors.teal,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter owner email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          UniversalFormField.spacing,

          // Address Field with Location Button
          Row(
            children: [
              Expanded(
                child: UniversalFormField.textField(
                  label: _AppStrings.addressLabel,
                  controller: _addressController,
                  prefixIcon: Icons.home,
                  prefixIconColor: Colors.brown,
                  validator: (value) => UniversalFormField.requiredValidator(value, 'address'),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: _isFetchingLocation ? null : _getCurrentLocation,
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(
                      vertical: 12, horizontal: 16),
                ),
                icon: _isFetchingLocation
                    ? const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.location_on),
                label: Text(
                    _isFetchingLocation ? 'Getting...' : 'Get Location'),
              ),
            ],
          ),

          // GPS Location Display
          if (_latitude != null && _longitude != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'GPS: ${_latitude!.toStringAsFixed(6)}, ${_longitude!.toStringAsFixed(6)}',
                style: const TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          UniversalFormField.spacing,

          // Cattle Count and Capacity Fields
          Row(
            children: [
              Expanded(
                child: UniversalFormField.numberField(
                  label: _AppStrings.cattleCountLabel,
                  controller: _cattleCountController,
                  allowDecimals: false,
                  prefixIcon: Icons.numbers,
                  prefixIconColor: Colors.indigo,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return _AppStrings.cattleCountInvalid;
                    }
                    final count = int.tryParse(value);
                    if (count == null || count < 0) {
                      return _AppStrings.cattleCountInvalid;
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: UniversalFormField.numberField(
                  label: _AppStrings.capacityLabel,
                  controller: _capacityController,
                  allowDecimals: false,
                  prefixIcon: Icons.domain,
                  prefixIconColor: Colors.red,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return _AppStrings.capacityInvalid;
                    }
                    final capacity = int.tryParse(value);
                    if (capacity == null || capacity < 0) {
                      return _AppStrings.capacityInvalid;
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      await _handleSubmit();

      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.farm != null
              ? _AppStrings.updateSuccess
              : _AppStrings.addSuccess,
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, '${_AppStrings.saveError}: $e');
      }
    }
  }
}
