import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../models/delivery_record_isar.dart';
import '../models/breeding_event_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../services/breeding_repository.dart';
import '../services/breeding_analytics_service.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class BreedingFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? breedingMethod;
  final String? status;

  const BreedingFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.breedingMethod,
    this.status,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (breedingMethod?.isNotEmpty ?? false) ||
      (status?.isNotEmpty ?? false);

  /// Create a copy with updated values
  BreedingFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? breedingMethod,
    String? status,
  }) {
    return BreedingFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      breedingMethod: breedingMethod ?? this.breedingMethod,
      status: status ?? this.status,
    );
  }

  /// Clear all filters
  static const BreedingFilterState empty = BreedingFilterState();
}

/// Reactive controller for the main breeding screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class BreedingController extends ChangeNotifier {
  // Repositories
  final BreedingRepository _breedingRepository = GetIt.instance<BreedingRepository>();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Following cattle module pattern
  StreamSubscription<List<BreedingRecordIsar>>? _breedingStreamSubscription;
  StreamSubscription<List<PregnancyRecordIsar>>? _pregnancyStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<BreedingRecordIsar> _unfilteredBreedingRecords = []; // Complete dataset for analytics calculations
  List<PregnancyRecordIsar> _unfilteredPregnancyRecords = []; // Complete pregnancy dataset
  final List<DeliveryRecordIsar> _unfilteredDeliveryRecords = []; // Complete delivery dataset
  final List<BreedingEventIsar> _unfilteredBreedingEvents = []; // Complete breeding events dataset
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics
  List<AnimalTypeIsar> _animalTypes = []; // Animal types for form dialogs

  List<BreedingRecordIsar> _filteredBreedingRecords = []; // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  BreedingAnalyticsResult _analyticsResult = BreedingAnalyticsResult.empty;

  // Filter state management - decoupled from UI
  BreedingFilterState _currentFilters = BreedingFilterState.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered breeding records for UI display
  /// This is what the BreedingRecordsTab should show
  List<BreedingRecordIsar> get breedingRecords => List.unmodifiable(_filteredBreedingRecords);

  /// Returns the complete unfiltered breeding records for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<BreedingRecordIsar> get unfilteredBreedingRecords => List.unmodifiable(_unfilteredBreedingRecords);

  /// Returns the complete unfiltered pregnancy records for analytics
  List<PregnancyRecordIsar> get unfilteredPregnancyRecords => List.unmodifiable(_unfilteredPregnancyRecords);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  BreedingAnalyticsResult get analytics => _analyticsResult;

  // Convenience getters for backward compatibility with UI
  int get totalBreedingRecords => _analyticsResult.totalBreedingRecords;
  int get totalPregnancies => _analyticsResult.totalPregnancies;
  int get totalDeliveries => _analyticsResult.totalDeliveries;
  int get activePregnancies => _analyticsResult.activePregnancies;

  // Additional getters for analytics tabs
  int get totalPregnancyRecords => _analyticsResult.totalPregnancies;
  int get femaleCattle => _unfilteredCattle.where((c) => c.gender == CattleGender.female).length;
  Map<String, int> get breedingByStatus => _analyticsResult.breedingByStatus;
  Map<String, int> get pregnancyByStatus => _analyticsResult.pregnancyByStatus;
  int get completedPregnancies => _analyticsResult.completedPregnancies;
  List<CattleIsar> get cattle => _unfilteredCattle;
  int get completedBreedings => _analyticsResult.completedBreedings;
  int get failedBreedings => _analyticsResult.failedBreedings;
  double get conceptionRate => _analyticsResult.conceptionRate;
  double get calvingRate => _analyticsResult.calvingRate;
  Map<String, int> get breedingMethodDistribution => _analyticsResult.breedingMethodDistribution;
  Map<String, int> get breedingStatusDistribution => _analyticsResult.breedingStatusDistribution;
  Map<String, int> get pregnancyStatusDistribution => _analyticsResult.pregnancyStatusDistribution;
  double get averageCalvingInterval => _analyticsResult.averageCalvingInterval;
  double get averageGestationPeriod => _analyticsResult.averageGestationPeriod;
  int get totalCalvesBorn => _analyticsResult.totalCalvesBorn;
  double get averageCalvesPerDelivery => _analyticsResult.averageCalvesPerDelivery;
  double get totalBreedingCosts => _analyticsResult.totalBreedingCosts;
  int get overduePregnancies => _analyticsResult.overduePregnancies;
  int get upcomingCalvings => _analyticsResult.upcomingCalvings;

  // Filter state access
  BreedingFilterState get currentFilters => _currentFilters;

  // Constructor
  BreedingController() {
    _initializeStreamListeners();
    _loadAnimalTypes();
  }

  /// Initialize stream listeners for real-time updates following cattle module pattern
  /// Using separate streams instead of StreamZip for better reliability
  void _initializeStreamListeners() {
    debugPrint('🔧 BREEDING CONTROLLER: Initializing stream listeners (cattle module pattern)...');

    // Breeding records stream - primary data source
    _breedingStreamSubscription = _isar.breedingRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((breedingRecords) {
      debugPrint('🔄 BREEDING STREAM: Received ${breedingRecords.length} breeding records');
      if (breedingRecords.isNotEmpty) {
        debugPrint('   First record ID: ${breedingRecords.first.businessId}');
      }
      _unfilteredBreedingRecords = breedingRecords;
      _updateAnalytics(); // Update analytics immediately when breeding records change
      _updateFilteredDataAndNotify();
    }, onError: (error) {
      debugPrint('❌ Breeding stream error: $error');
    });

    // Pregnancy records stream
    _pregnancyStreamSubscription = _isar.pregnancyRecordIsars.where()
        .watch(fireImmediately: true)
        .listen((pregnancyRecords) {
      debugPrint('🔄 PREGNANCY STREAM: Received ${pregnancyRecords.length} pregnancy records');
      _unfilteredPregnancyRecords = pregnancyRecords;
      _updateAnalytics();
    }, onError: (error) {
      debugPrint('❌ Pregnancy stream error: $error');
    });

    // Cattle stream
    _cattleStreamSubscription = _isar.cattleIsars.where()
        .watch(fireImmediately: true)
        .listen((cattle) {
      debugPrint('🔄 CATTLE STREAM: Received ${cattle.length} cattle records');
      _unfilteredCattle = cattle;
      _updateAnalytics();
    }, onError: (error) {
      debugPrint('❌ Cattle stream error: $error');
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _hasActiveFilters = false;

    debugPrint('✅ BREEDING CONTROLLER: Stream listeners initialized');
  }



  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = BreedingAnalyticsService.calculate(
      _unfilteredBreedingRecords, // Use unfiltered data for accurate analytics
      _unfilteredPregnancyRecords,
      _unfilteredDeliveryRecords,
      _unfilteredBreedingEvents,
      _unfilteredCattle,
    );
  }

  /// Update filtered data and notify listeners - following cattle module pattern
  void _updateFilteredDataAndNotify() {
    try {
      debugPrint('📊 BREEDING CONTROLLER: Updating filtered data and UI');

      // Update filtered data if no filters are active
      if (!_hasActiveFilters) {
        _filteredBreedingRecords = List.from(_unfilteredBreedingRecords);
        _setState(ControllerState.loaded);
      }

      debugPrint('🔔 BREEDING CONTROLLER: Notifying UI listeners');
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating filtered data: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update breeding data: $e';
      notifyListeners();
    }
  }

  /// Update analytics calculations
  void _updateAnalytics() async {
    try {
      _calculateAnalytics();
    } catch (e) {
      debugPrint('Error updating analytics: $e');
    }
  }

  /// Refresh all data - compatible with analytics tab refresh functionality
  Future<void> refresh() async {
    try {
      // Test: Manually query the database to see if records exist
      debugPrint('🔍 BREEDING CONTROLLER: Testing direct database query...');
      final directQuery = await _isar.breedingRecordIsars.where().findAll();
      debugPrint('📊 BREEDING CONTROLLER: Direct query found ${directQuery.length} records');
      if (directQuery.isNotEmpty) {
        debugPrint('   First record: ${directQuery.first.businessId}');
      }

      // Force analytics recalculation on unfiltered data
      if (_unfilteredBreedingRecords.isNotEmpty || _unfilteredPregnancyRecords.isNotEmpty) {
        _calculateAnalytics();
      }

      // Notify listeners of the refresh
      debugPrint('🔔 Main breeding controller: Notifying UI listeners (refresh complete)');
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing breeding data: $e\n$stackTrace');
      throw Exception('Failed to refresh breeding data: ${e.toString()}');
    }
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(BreedingFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredBreedingRecords = List.from(_unfilteredBreedingRecords);
      debugPrint('🔔 Main breeding controller: Notifying UI listeners (no filters applied)');
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? breedingMethod,
    String? status,
  }) {
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      breedingMethod: breedingMethod,
      status: status,
    );
    applyFilters(newFilters);
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(BreedingFilterState.empty);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<BreedingRecordIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredBreedingRecords = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      debugPrint('🔔 Main breeding controller: Notifying UI listeners (filtered data update)');
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(BreedingFilterState filterState) {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.breedingRecordIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .notesContains(searchTerm, caseSensitive: false)
          .or()
          .methodContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filterState.startDate != null) {
      currentQuery = currentQuery.filter().dateGreaterThan(filterState.startDate!);
    }
    if (filterState.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().dateLessThan(inclusiveEndDate);
    }

    // Apply breeding method filter
    if (filterState.breedingMethod?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().methodEqualTo(filterState.breedingMethod);
    }

    // Apply status filter
    if (filterState.status?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().statusEqualTo(filterState.status);
    }

    // Apply sorting at database level for optimal performance
    currentQuery = currentQuery.sortByDateDesc(); // Default sort by date (newest first)

    return currentQuery;
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new breeding record - only updates database, stream handles UI update
  Future<void> addBreedingRecord(BreedingRecordIsar record) async {
    debugPrint('🎯 BREEDING CONTROLLER: Adding breeding record');
    debugPrint('   Record ID: ${record.businessId}');
    debugPrint('   Cattle ID: ${record.cattleId}');
    debugPrint('   Method: ${record.method}');

    await _breedingRepository.saveBreedingRecord(record);
    debugPrint('✅ BREEDING CONTROLLER: Breeding record saved to repository');
    debugPrint('   Stream will handle UI update automatically...');
  }

  /// Add new pregnancy record - only updates database, stream handles UI update
  Future<void> addPregnancyRecord(PregnancyRecordIsar record) async {
    await _breedingRepository.savePregnancyRecord(record);
    // Stream will handle the UI update automatically
  }

  /// Add new delivery record - only updates database, stream handles UI update
  Future<void> addDeliveryRecord(DeliveryRecordIsar record) async {
    await _breedingRepository.saveDeliveryRecord(record);
    // Stream will handle the UI update automatically
  }

  /// Update breeding record - only updates database, stream handles UI update
  Future<void> updateBreedingRecord(BreedingRecordIsar updatedRecord) async {
    await _breedingRepository.saveBreedingRecord(updatedRecord);
    // Stream will handle the UI update automatically
  }

  /// Update pregnancy record - only updates database, stream handles UI update
  Future<void> updatePregnancyRecord(PregnancyRecordIsar updatedRecord) async {
    await _breedingRepository.savePregnancyRecord(updatedRecord);
    // Stream will handle the UI update automatically
  }

  /// Delete breeding record - only updates database, stream handles UI update
  Future<void> deleteBreedingRecord(String businessId) async {
    debugPrint('🎯 BREEDING CONTROLLER: Deleting breeding record with business ID: $businessId');
    // Find the breeding record by businessId first, then delete by Isar ID
    final record = _unfilteredBreedingRecords.firstWhere(
      (r) => r.businessId == businessId,
      orElse: () => throw Exception('Breeding record not found'),
    );
    await _breedingRepository.deleteBreedingRecord(record.id);
    debugPrint('✅ BREEDING CONTROLLER: Delete completed, stream will handle UI update...');
  }

  /// Delete pregnancy record - only updates database, stream handles UI update
  Future<void> deletePregnancyRecord(String businessId) async {
    debugPrint('🎯 BREEDING CONTROLLER: Deleting pregnancy record with business ID: $businessId');
    // Find the pregnancy record by businessId first, then delete by Isar ID
    final record = _unfilteredPregnancyRecords.firstWhere(
      (r) => r.businessId == businessId,
      orElse: () => throw Exception('Pregnancy record not found'),
    );
    await _breedingRepository.deletePregnancyRecord(record.id);
    debugPrint('✅ BREEDING CONTROLLER: Pregnancy delete completed, stream will handle UI update...');
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleId) {
    if (cattleId == null) return null;
    try {
      return _unfilteredCattle.firstWhere(
        (cattle) => cattle.tagId == cattleId, // Use tagId for consistency
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleId) {
    final cattle = getCattle(cattleId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  List<CattleIsar> get femaleCattleList {
    return _unfilteredCattle.where((c) =>
      c.gender == CattleGender.female &&
      c.status == CattleStatus.active
    ).toList();
  }

  Map<String, AnimalTypeIsar> get animalTypesMap {
    return {
      for (var type in _animalTypes)
        if (type.businessId != null) type.businessId!: type
    };
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  // _setError method removed - unused

  /// Load animal types for form dialogs
  Future<void> _loadAnimalTypes() async {
    try {
      final animalTypesData = await _farmSetupRepository.getAllAnimalTypes();
      _animalTypes = animalTypesData;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading animal types: $e');
      // Don't throw error, just log it - animal types are not critical for basic functionality
    }
  }

  @override
  void dispose() {
    // Cancel all stream subscriptions - following cattle module pattern
    _breedingStreamSubscription?.cancel();
    _pregnancyStreamSubscription?.cancel();
    _cattleStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
